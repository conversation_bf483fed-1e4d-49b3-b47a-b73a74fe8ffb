/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import java.io.IOException;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.WebServiceCallException;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.impl.JWTToken;
import com.stpl.tech.master.core.external.cache.SessionCache;
import com.stpl.tech.master.core.service.model.RequestData;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.JSONSerializer;
import org.springframework.web.client.HttpStatusCodeException;

public class AbstractResources extends AbstractExceptionHandler {

	private static final Logger LOG = LoggerFactory.getLogger(AbstractResources.class);

	@Autowired
	protected SessionCache sessionCache;

	@Autowired
	private TokenService<JWTToken> jwtService;

	public <T> Response createOKResponse(T object) {
		return createResponse(Response.Status.OK, object);
	}

	public <T> Response createResponse(Response.Status status, T object) {
		return Response.status(status).entity(object).build();
	}

	public void validate(UserSessionDetail sessionDetail) throws AuthenticationFailureException {
		if (sessionDetail == null) {
			throw new AuthenticationFailureException("Invalid Session");
		}
		validateSession(sessionDetail.getUnitId(), sessionDetail.getTerminalId(), sessionDetail.getUserId(),
				sessionDetail.getSessionKeyId());
	}

	public void validateSession(int unitId, Integer terminalId, int userId, String sessionKey)
			throws AuthenticationFailureException {
		boolean isValidated = sessionCache.validateSession(sessionKey, unitId, userId);
		if (!isValidated) {
			throw new AuthenticationFailureException(
					String.format("Not able to validate the session for the userId %d", userId));
		}
	}

	public RequestData getRequestData(String request, boolean validate) throws AuthenticationFailureException {
		RequestData data = JSONSerializer.toJSON(request, RequestData.class);
		if (validate) {
			validate(data.getSession());
		}
		return data;
	}

	public <T> T getData(String request, Class<T> clazz) throws AuthenticationFailureException {
		return getData(getRequestData(request, true), clazz);
	}

	public <T> T getData(RequestData request, Class<T> clazz) throws AuthenticationFailureException {
		return JSONSerializer.toJSON(request.getData(), clazz);
	}

	public <T> T callWebService(Class<T> clazz, String endPoint, Object object) throws WebServiceCallException {
		try {
			HttpResponse response = createPostRequest(endPoint, object);
			return WebServiceHelper.convertResponse(response, clazz, true);
		} catch (IOException e) {
			e.printStackTrace();
		} catch (HttpStatusCodeException e) {
			e.printStackTrace();
			throw new WebServiceCallException(e.getResponseBodyAsString());
		}
		return null;
	}

	public <T> T callWebServiceWithTimeout(Class<T> clazz, String endPoint, Object object,int socketTimeOut, int connectionTimeout ) throws WebServiceCallException {
		try {
			HttpResponse response = createPostRequestWithTimeout(endPoint, object, socketTimeOut, connectionTimeout);
			return WebServiceHelper.convertResponse(response, clazz, true);
		} catch (IOException e) {
			e.printStackTrace();
		} catch (HttpStatusCodeException e) {
			e.printStackTrace();
			throw new WebServiceCallException(e.getResponseBodyAsString());
		}
		return null;
	}
	
	public <T> T callWebServiceWithAuth(Class<T> clazz, String endPoint, String token,Object object) {
		try {
			HttpResponse response = createPostRequest(endPoint, object);
			return WebServiceHelper.convertResponse(response, clazz, true);
		} catch (Exception e) {
			LOG.error("Error while creating web request to {}", endPoint, e);
		}
		return null;
	}

	public HttpResponse createPostRequest(String url, Object object) throws ClientProtocolException, IOException {
		HttpPost requestObject = new HttpPost(url);
		String consumptionDataJson = WebServiceHelper.convertToString(object);
		HttpEntity httpEntity = new StringEntity(consumptionDataJson, AppConstants.CHARSET);
		requestObject.setHeader("Content-type", MediaType.APPLICATION_JSON.toString());
		requestObject.setEntity(httpEntity);
		return WebServiceHelper.postRequestWithNoTimeout(requestObject);
	}

	public HttpResponse createPostRequestWithTimeout(String url, Object object,int socketTimeOut, int connectionTimeout) throws ClientProtocolException, IOException {
		HttpPost requestObject = new HttpPost(url);
		String consumptionDataJson = WebServiceHelper.convertToString(object);
		HttpEntity httpEntity = new StringEntity(consumptionDataJson, AppConstants.CHARSET);
		requestObject.setHeader("Content-type", MediaType.APPLICATION_JSON.toString());
		requestObject.setEntity(httpEntity);
		return WebServiceHelper.postRequest(requestObject, socketTimeOut, connectionTimeout);
	}
	
	public HttpResponse createPostRequestWithAuth(String url,String token, Object object) throws ClientProtocolException, IOException {
		HttpPost requestObject = new HttpPost(url);
		String consumptionDataJson = WebServiceHelper.convertToString(object);
		HttpEntity httpEntity = new StringEntity(consumptionDataJson, AppConstants.CHARSET);
		requestObject.setHeader("Content-type", MediaType.APPLICATION_JSON.toString());
		requestObject.setHeader(WebServiceHelper.AUTH_INTERNAL, token);
		requestObject.setEntity(httpEntity);
		return WebServiceHelper.postRequestWithNoTimeout(requestObject);
	}

	public Integer getLoggedInUser(HttpServletRequest request) {
		try {
			String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
			if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
				JWTToken jwtToken = new JWTToken();
				jwtService.parseToken(jwtToken, authHeader);
				return Integer.valueOf(jwtToken.getUserId());
			}
		} catch (Exception e) {
		}
		return -1;
	}

	public Integer getLoggedInUnit(HttpServletRequest request) {
		try {
			String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
			if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
				JWTToken jwtToken = new JWTToken();
				jwtService.parseToken(jwtToken, authHeader);
				return Integer.valueOf(jwtToken.getUnitId());
			}
		} catch (Exception e) {
		}
		return -1;
	}


	public static Integer getBrandIdFromHeaders(HttpServletRequest httpServletRequest){
		return  Objects.nonNull(httpServletRequest.getHeader("Bid")) ?
				Integer.valueOf(httpServletRequest.getHeader("Bid").trim())
				: AppConstants.CHAAYOS_BRAND_ID;
	}

}
