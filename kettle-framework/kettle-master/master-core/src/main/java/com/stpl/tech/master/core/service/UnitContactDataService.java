package com.stpl.tech.master.core.service;

import com.stpl.tech.master.domain.model.UnitContactData;
import com.stpl.tech.master.domain.model.UnitContactDetails;

import java.util.List;

/**
 * Service interface for managing unit contact data
 */
public interface UnitContactDataService {

    /**
     * Retrieves contact data for a specific unit
     * 
     * @param unitId the unit ID to fetch contacts for
     * @return list of unit contact details
     */
    List<UnitContactDetails> getUnitContactData(Integer unitId);

    /**
     * Adds or updates unit contact data in bulk
     * 
     * @param unitContactData the contact data to save/update
     * @param loggedInUser the user performing the operation
     * @return true if operation was successful, false otherwise
     */
    boolean addOrUpdateUnitContactData(UnitContactData unitContactData, String loggedInUser);

    /**
     * Clears all cached contact data
     */
    void clearCache();

    /**
     * Clears cached contact data for a specific unit
     * 
     * @param unitId the unit ID to clear cache for
     */
    void clearCacheForUnit(Integer unitId);

    /**
     * Gets the current cache size
     * 
     * @return number of units in cache
     */
    int getCacheSize();
}
