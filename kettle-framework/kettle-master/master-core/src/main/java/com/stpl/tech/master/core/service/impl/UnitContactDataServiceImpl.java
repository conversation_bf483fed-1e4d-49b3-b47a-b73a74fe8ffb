package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.dao.UnitContactDataDao;
import com.stpl.tech.master.core.service.UnitContactDataService;
import com.stpl.tech.master.data.model.UnitContactDataEntity;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.UnitContactData;
import com.stpl.tech.master.domain.model.UnitContactDetails;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class UnitContactDataServiceImpl implements UnitContactDataService {

    private static final Logger LOG = LoggerFactory.getLogger(UnitContactDataServiceImpl.class);

    @Autowired
    private UnitContactDataDao unitContactDataDao;

    // Local cache using ConcurrentHashMap for thread safety
    private final Map<Integer, List<UnitContactDataEntity>> unitContactCache = new ConcurrentHashMap<>();
    private final Map<Integer, Long> cacheTimestamps = new ConcurrentHashMap<>();
    private static final long CACHE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitContactDetails> getUnitContactData(Integer unitId) {
        LOG.info("Getting contact data for unit: {}", unitId);
        
        try {
            List<UnitContactDataEntity> contactEntities = getContactsFromCacheOrDb(unitId);
            
            if (contactEntities.isEmpty()) {
                LOG.info("No contact data found for unit: {}", unitId);
                return new ArrayList<>();
            }

            // Group by unit and convert to domain objects
            Map<Integer, List<UnitContactDataEntity>> groupedByUnit = contactEntities.stream()
                    .collect(Collectors.groupingBy(UnitContactDataEntity::getUnitId));

            List<UnitContactDetails> result = new ArrayList<>();
            
            for (Map.Entry<Integer, List<UnitContactDataEntity>> entry : groupedByUnit.entrySet()) {
                Integer currentUnitId = entry.getKey();
                List<UnitContactDataEntity> contacts = entry.getValue();
                
                // Filter only active contacts
                List<IdCodeName> activeContacts = contacts.stream()
                        .filter(contact -> "ACTIVE".equals(contact.getStatus()))
                        .map(this::convertToIdCodeName)
                        .collect(Collectors.toList());

                UnitContactDetails unitContactDetails = new UnitContactDetails();
                unitContactDetails.setUnitId(currentUnitId);
                unitContactDetails.setUnitName(getUnitName(currentUnitId)); // You may need to implement this
                unitContactDetails.setContactDetails(activeContacts);
                
                result.add(unitContactDetails);
            }

            LOG.info("Retrieved {} unit contact details for unit: {}", result.size(), unitId);
            return result;
            
        } catch (Exception e) {
            LOG.error("Error retrieving contact data for unit: {}", unitId, e);
            throw new RuntimeException("Failed to retrieve contact data", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addOrUpdateUnitContactData(UnitContactData unitContactData, Integer loggedInUser) {
        LOG.info("Saving contact data for unit: {} by user: {}", unitContactData.getUnitId(), loggedInUser);
        
        try {
            Integer unitId = unitContactData.getUnitId();
            List<UnitContactData.ContactInfo> contacts = unitContactData.getContacts();
            
            if (contacts == null || contacts.isEmpty()) {
                LOG.warn("No contacts provided for unit: {}", unitId);
                return false;
            }

            // Get existing contacts from database
            List<UnitContactDataEntity> existingContacts = unitContactDataDao.getActiveContactsByUnitId(unitId);
            Map<Integer, UnitContactDataEntity> existingContactMap = existingContacts.stream()
                    .collect(Collectors.toMap(UnitContactDataEntity::getId, contact -> contact));

            List<UnitContactDataEntity> contactsToSave = new ArrayList<>();
            LocalDateTime currentTime = LocalDateTime.now();

            // Process each contact in the request
            for (UnitContactData.ContactInfo contactInfo : contacts) {
                UnitContactDataEntity entity;
                
                if (contactInfo.getId() != null && existingContactMap.containsKey(contactInfo.getId())) {
                    // Update existing contact
                    entity = existingContactMap.get(contactInfo.getId());
                    entity.setReferenceName(contactInfo.getReferenceName());
                    entity.setContactNumber(contactInfo.getContactNumber());
                    entity.setStatus(contactInfo.getStatus());
                    entity.setUpdatedBy(loggedInUser);
                    entity.setUpdatedOn(currentTime);
                } else {
                    // Create new contact
                    entity = new UnitContactDataEntity();
                    entity.setUnitId(unitId);
                    entity.setReferenceName(contactInfo.getReferenceName());
                    entity.setContactNumber(contactInfo.getContactNumber());
                    entity.setStatus(contactInfo.getStatus());
                    entity.setCreatedBy(loggedInUser);
                    entity.setCreatedOn(currentTime);
                    entity.setUpdatedBy(loggedInUser);
                    entity.setUpdatedOn(currentTime);
                }
                
                contactsToSave.add(entity);
            }

            // Bulk save/update
            boolean success = unitContactDataDao.saveOrUpdateContacts(contactsToSave);
            
            if (success) {
                // Invalidate cache for this unit
                invalidateCache(unitId);
                LOG.info("Successfully saved {} contacts for unit: {}", contactsToSave.size(), unitId);
            } else {
                LOG.error("Failed to save contacts for unit: {}", unitId);
            }
            
            return success;
            
        } catch (Exception e) {
            LOG.error("Error saving contact data for unit: {} by user: {}", 
                     unitContactData.getUnitId(), loggedInUser, e);
            throw new RuntimeException("Failed to save contact data", e);
        }
    }

    private List<UnitContactDataEntity> getContactsFromCacheOrDb(Integer unitId) {
        // Check cache first
        if (isCacheValid(unitId)) {
            LOG.debug("Returning cached contact data for unit: {}", unitId);
            return unitContactCache.get(unitId);
        }

        // Fetch from database
        LOG.debug("Fetching contact data from database for unit: {}", unitId);
        List<UnitContactDataEntity> contacts = unitContactDataDao.getContactsByUnitId(unitId);
        
        // Update cache
        updateCache(unitId, contacts);
        
        return contacts;
    }

    private boolean isCacheValid(Integer unitId) {
        if (!unitContactCache.containsKey(unitId) || !cacheTimestamps.containsKey(unitId)) {
            return false;
        }
        
        long cacheTime = cacheTimestamps.get(unitId);
        return (System.currentTimeMillis() - cacheTime) < CACHE_EXPIRY_MS;
    }

    private void updateCache(Integer unitId, List<UnitContactDataEntity> contacts) {
        unitContactCache.put(unitId, new ArrayList<>(contacts));
        cacheTimestamps.put(unitId, System.currentTimeMillis());
        LOG.debug("Updated cache for unit: {} with {} contacts", unitId, contacts.size());
    }

    private void invalidateCache(Integer unitId) {
        unitContactCache.remove(unitId);
        cacheTimestamps.remove(unitId);
        LOG.debug("Invalidated cache for unit: {}", unitId);
    }

    private IdCodeName convertToIdCodeName(UnitContactDataEntity entity) {
        IdCodeName idCodeName = new IdCodeName();
        idCodeName.setId(entity.getId());
        idCodeName.setCode(entity.getReferenceName());
        idCodeName.setName(entity.getContactNumber().toString());
        return idCodeName;
    }

    private String getUnitName(Integer unitId) {
        // TODO: Implement unit name lookup from unit service or cache
        // For now, return a placeholder
        return "Unit " + unitId;
    }

    // Cache management methods
    public void clearCache() {
        unitContactCache.clear();
        cacheTimestamps.clear();
        LOG.info("Cleared all unit contact cache");
    }

    public void clearCacheForUnit(Integer unitId) {
        invalidateCache(unitId);
        LOG.info("Cleared cache for unit: {}", unitId);
    }

    public int getCacheSize() {
        return unitContactCache.size();
    }
}
