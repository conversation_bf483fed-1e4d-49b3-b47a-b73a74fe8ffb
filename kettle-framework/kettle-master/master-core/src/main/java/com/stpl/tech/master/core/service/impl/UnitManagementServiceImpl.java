/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.impl;

import com.hazelcast.map.IMap;
import com.stpl.tech.master.UnitStatusData;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.SCMServiceEndpoints;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.activity.service.ActivityLoggerService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.core.service.MasterMetadataService;
import com.stpl.tech.master.core.service.UnitManagementService;
import com.stpl.tech.master.data.dao.UnitBrandMappingDao;
import com.stpl.tech.master.data.dao.UnitManagementDao;
import com.stpl.tech.master.data.dao.UnitWSToStationCategoryMappingDao;
import com.stpl.tech.master.data.model.ApplicationVersionDetail;
import com.stpl.tech.master.data.model.ApplicationVersionDetailData;
import com.stpl.tech.master.data.model.ApplicationVersionEvent;
import com.stpl.tech.master.data.model.BusinessDivision;
import com.stpl.tech.master.data.model.CacheReferenceMetadata;
import com.stpl.tech.master.data.model.Denomination;
import com.stpl.tech.master.data.model.EntityAliasMappingData;
import com.stpl.tech.master.data.model.EventStatusType;
import com.stpl.tech.master.data.model.FeedbackQuestionsDetail;
import com.stpl.tech.master.data.model.FeedbackQuestionsUnitMapping;
import com.stpl.tech.master.data.model.PaymentMode;
import com.stpl.tech.master.data.model.TaxProfile;
import com.stpl.tech.master.data.model.UnitBrandMapping;
import com.stpl.tech.master.data.model.UnitContactDetailsData;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UnitIpAddressData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitProductMapping;
import com.stpl.tech.master.data.model.UnitProductPricing;
import com.stpl.tech.master.data.model.UnitProductSyncLog;
import com.stpl.tech.master.data.model.UnitToPartnerDqrMapping;
import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import com.stpl.tech.master.data.model.UnitWSToStationCategoryMappingData;
import com.stpl.tech.master.data.model.VersionCompatibilityData;
import com.stpl.tech.master.data.repository.ApplicationVersionDao;
import com.stpl.tech.master.data.repository.ApplicationVersionDetailDataDao;
import com.stpl.tech.master.data.repository.ApplicationVersionEventDao;
import com.stpl.tech.master.data.repository.CacheReferenceMetadataDao;
import com.stpl.tech.master.data.repository.FeedBackQuestionDetailDao;
import com.stpl.tech.master.data.repository.FeedBackQuestionsUnitMappingDao;
import com.stpl.tech.master.data.repository.UnitContactDetailsDao;
import com.stpl.tech.master.data.repository.UnitIpAddressDao;
import com.stpl.tech.master.data.repository.UnitToPartnerDqrMappingDao;
import com.stpl.tech.master.data.repository.UnitToPartnerEdcMappingDao;
import com.stpl.tech.master.data.repository.VersionCompatibilityDao;
import com.stpl.tech.master.domain.model.ApkUploadResponse;
import com.stpl.tech.master.domain.model.Application;
import com.stpl.tech.master.domain.model.ApplicationVersionDomian;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.BrandMapping;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.EntityAliasKey;
import com.stpl.tech.master.domain.model.FeedbackQuesMappingDomain;
import com.stpl.tech.master.domain.model.FeedbackQuestionDomain;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitContactDetails;
import com.stpl.tech.master.domain.model.UnitIpAddressRequest;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitPaymentModeMappingDetail;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.domain.model.UnitToPartnerEdcMappingDetail;
import com.stpl.tech.master.domain.model.UnitVersionDetail;
import com.stpl.tech.master.domain.model.UnitVersionDomain;
import com.stpl.tech.master.domain.model.UnitWsToStationCategoryMappingRequest;
import com.stpl.tech.master.domain.model.VersionEventDomain;
import com.stpl.tech.master.monk.configuration.model.MonkAttr;
import com.stpl.tech.master.monk.configuration.model.MonkConfiguration;
import com.stpl.tech.master.monk.configuration.model.MonkConfigurationValue;
import com.stpl.tech.master.readonly.domain.model.ProductPriceVO;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.master.readonly.domain.model.UnitProductData;
import com.stpl.tech.master.recipe.read.model.CompositeIngredientDataVO;
import com.stpl.tech.master.recipe.read.model.CompositeProductDataVO;
import com.stpl.tech.master.recipe.read.model.IngredientDetailVO;
import com.stpl.tech.master.recipe.read.model.IngredientProductDetailVO;
import com.stpl.tech.master.recipe.read.model.ProductDataVO;
import com.stpl.tech.master.recipe.read.model.RecipeDetailVO;
import com.stpl.tech.spring.exception.FileArchiveServiceException;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.RequestContext;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.stpl.tech.util.AppUtils.generateIpAddressCacheKey;

@Service
public class UnitManagementServiceImpl implements UnitManagementService {
    private static final Logger LOG = LoggerFactory.getLogger(UnitManagementServiceImpl.class);

    @Autowired
    private UnitManagementDao dao;

    @Autowired
    private UnitToPartnerEdcMappingDao unitToPartnerEdcMappingDao;

    @Autowired
    private MasterDataCacheService cache;

    @Autowired
    private MasterProperties props;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private CacheReferenceMetadataDao cacheReferenceMetadataDao;

    @Autowired
    private UnitIpAddressDao unitIpAddressDao;

    @Autowired
    private ApplicationVersionEventDao versionEventDao;

    @Autowired
    private ApplicationVersionDao applicationVersionDao;
    @Autowired
    private ApplicationVersionDetailDataDao applicationVersionDetailDataDao;
    @Autowired
    private FileArchiveService fileArchiveService;
    @Autowired
    private VersionCompatibilityDao versionCompatibilityDao;
    @Autowired
    private FeedBackQuestionDetailDao feedBackQuestionDetailDao;
    @Autowired
    private FeedBackQuestionsUnitMappingDao feedBackQuestionsUnitMappingDao;

    @Autowired
    private UnitToPartnerDqrMappingDao unitToPartnerDqrMappingDao;

    @Autowired
    private UnitWSToStationCategoryMappingDao unitWSToStationCategoryMappingDao;

    @Autowired
    private UnitBrandMappingDao unitBrandMappingDao;

    @Autowired
    private UnitContactDetailsDao unitContactDetailsDao;
    @Autowired
    private MasterMetadataService masterService;
    @Autowired
    private ActivityLoggerService loggerService;
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Unit addUnit(Unit unit) throws DataUpdationException, DataNotFoundException {
        Unit data = dao.addUnit(unit);
        cache.addUnit(data);
        return data;
    }

    private void addSCMUnit(UnitBasicDetail data) {
        String endPoint = props.getSCMEndpoint() + "unit-management/unit";
        try {
            HttpResponse response = createPostRequest(endPoint, data);
            WebServiceHelper.convertResponse(response, Integer.class);
        } catch (Exception e) {
            LOG.error("Error while creating web request to {}", endPoint, e);
        }
    }

    private HttpResponse deactivateSCMUnit(Integer data) throws DataUpdationException, IOException {
        String endPoint = props.getSCMEndpoint() + "rest/v1/unit-management/unit-deactivate";
        Map<String,Object> obj = new HashMap<>();
        HttpResponse response = null;
        try{
            //response = WebServiceHelper.postRequestWithAuthInternal(endPoint,props.getSCMAuthInternal(),data);
            obj =WebServiceHelper.postRequestWithAuthInternalWithTimeout(endPoint,data,Map.class,props.getSCMAuthInternal());
        }catch (Exception e){
            String msg = "Error while Deactivating Unit On SUMO ";
            LOG.error(msg,e);
            throw new DataUpdationException(msg);
        }
        Boolean isValid = obj.get("canBeDeActivated").equals(true) ;
        if(isValid.equals(Boolean.FALSE)){
            throw new DataUpdationException((String) obj.get("message"));
        }

        return response;
    }

    private HttpResponse createPostRequest(String url, Object object) throws ClientProtocolException, IOException {
        HttpPost requestObject = new HttpPost(url);
        String consumptionDataJson = WebServiceHelper.convertToString(object);
        HttpEntity httpEntity = new StringEntity(consumptionDataJson, "UTF-8");
        requestObject.setHeader("Content-type", MediaType.APPLICATION_JSON.toString());
        requestObject.setEntity(httpEntity);
        return WebServiceHelper.postRequest(requestObject);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Unit updateUnit(Unit unit) throws DataUpdationException, DataNotFoundException {
        Unit data = dao.updateUnit(unit);
        cache.addUnit(data);
        return data;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateUnitBusinessHours(CafeTimingChangeRequest request) throws DataUpdationException, DataNotFoundException{
        try {
            return dao.updateUnitBusinessHours(request);
        }
        catch (Exception e){
            LOG.info("Error Updating business hours::{}",e);
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Unit changeUnitStatus(int unitId, UnitStatus status) throws DataUpdationException, DataNotFoundException, IOException {
        Unit data = dao.changeUnitStatus(unitId, status);
        data.setStatus(status);
        if(status.equals(UnitStatus.IN_ACTIVE)){
            deactivateSCMUnit(data.getId());
        }
        cache.addUnit(data);


        //addSCMUnit(cache.addUnit(data));
        return data;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Unit changeUnitStatusForDineInAndChaayos(int unitId, Boolean dineIn, Boolean chaayos) throws DataUpdationException, DataNotFoundException {
        Unit data = dao.changeUnitStatusForDineInAndChaayos(unitId,dineIn,chaayos);
        if(dineIn!=null){
            data.setCafeAppStatus(dineIn==true?AppConstants.ACTIVE :AppConstants.IN_ACTIVE);
        }
        if(chaayos!=null){
            data.setCafeNeoStatus(chaayos==true?AppConstants.ACTIVE :AppConstants.IN_ACTIVE);
        }
        cache.addUnit(data);
        // addSCMUnit(cache.addUnit(data));
        return data;
    }



    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Unit updateProductMapping(int unitId, List<ProductPrice> prices) throws DataUpdationException {
        return dao.updateProductMapping(unitId, prices);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BusinessDivision getBusinessDivision(int businessDivId) {
        return dao.getBusinessDivision(businessDivId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public TaxProfile getTaxProfile(int taxProfileId) {
        return dao.getTaxProfile(taxProfileId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public MonkConfiguration addMonkConfiguration(MonkConfiguration configuration) {
        return dao.addMonkConfiguration(configuration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<MonkAttr> getMonkMetadata() {
        return dao.getMonkMetadata();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<MonkConfigurationValue> getUnitMonkConfigurationData(int unitId) {
        return dao.getUnitMonkConfigurationData(unitId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void changeUnitLiveStatus(int unitId, boolean status) throws DataNotFoundException {
        Unit unit = dao.changeUnitLiveStatus(unitId, status);
        unit.setLive(status);
        unit.setFaDaycloseEnabled(AppUtils.setStatus(status));
        cache.addUnit(unit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateUnitF9Flag(Integer unitId) {
        try {
            Unit unit = dao.updateUnitF9Flag(unitId);
            cache.addUnit(unit);
        }
        catch (Exception e) {
            LOG.error("Exception Occurred while updating the F9 Enabled flag for unit {} ::: ",unitId,e);
        }
    }

    @Override
    public UnitProductData setProductAliases(UnitProductData unitProductData, int brandId) {
        Collection<ProductVO> productVOColletion = unitProductData.getProducts();
        Collection<ProductVO> list = new ArrayList<>();
        for (ProductVO product : productVOColletion) {
            ProductVO product1 = setAlias(product, brandId);
            drillDownAlias(product1, brandId);
            list.add(product1);
        }
        unitProductData.getProducts().clear();
        unitProductData.getProducts().addAll(list);
        return unitProductData;
    }

    @Override
    public Integer getPriceProfileUnitId(Integer brandId, int unitId, Integer partnerId) {
        UnitPartnerBrandKey unitPartner = new UnitPartnerBrandKey(unitId, brandId, partnerId);
        UnitPartnerBrandMappingData unitPartnerBrandMappingData = masterCache.getUnitPartnerBrandMappingMetaData().get(unitPartner);
        return unitPartnerBrandMappingData.getPriceProfileUnitId();
    }

    // TODO THERE IS A BETTER WAY TO DO THIS
    private void drillDownAlias(ProductVO product, int brandId) {
        if (product.getPrices() != null) {
            List<ProductPriceVO> priceVOS = product.getPrices();
            if (priceVOS != null) {
                for (ProductPriceVO priceVO : priceVOS) {
                    RecipeDetailVO recipeDetailVO = priceVO.getRecipe();
                    if (recipeDetailVO != null) {
                        IngredientDetailVO ingredient = recipeDetailVO.getIngredient();
                        if (ingredient != null) {
                            CompositeProductDataVO compositeProduct = ingredient.getCompositeProduct();
                            if (compositeProduct != null) {
                                List<CompositeIngredientDataVO> details = compositeProduct.getDetails();
                                if (details != null) {
                                    for (CompositeIngredientDataVO detail : details) {
                                        List<IngredientProductDetailVO> menuProducts = detail.getMenuProducts();
                                        if (menuProducts != null) {
                                            for (IngredientProductDetailVO menuProduct : menuProducts) {
                                                ProductDataVO product1 = menuProduct.getProduct();
                                                if (product1 != null) {
                                                    product1 = setAlias(product1, brandId);
                                                }
                                                menuProduct.setProduct(product1);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        List<IngredientProductDetailVO> addons = recipeDetailVO.getAddons();
                        if (addons != null) {
                            for (IngredientProductDetailVO addon : addons) {
                                ProductDataVO product1 = addon.getProduct();
                                if (product1 != null) {
                                    product1 = setAlias(product1, brandId);
                                }
                                addon.setProduct(product1);
                            }
                        }
                    }
                }
            }
        }
    }

    private ProductDataVO setAlias(ProductDataVO productDataVO, int brandId) {
        EntityAliasKey entityAliasKey = new EntityAliasKey(productDataVO.getProductId(), "PRODUCT", brandId);
        EntityAliasMappingData aliasMappingData = masterCache.getEntityAliasMappingData().get(entityAliasKey);

        if (aliasMappingData != null) {
            productDataVO = setAlias(productDataVO, aliasMappingData);
        }
        return productDataVO;
    }

    private ProductDataVO setAlias(ProductDataVO product, EntityAliasMappingData aliasMappingData) {
        ProductDataVO cloned = null;
        try {
            cloned = (ProductDataVO) product.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        if (aliasMappingData.getAlias() != null && aliasMappingData.getAlias().length() > 0) {
            cloned.setName(aliasMappingData.getAlias());
        }
        return cloned;
    }

    private ProductVO setAlias(ProductVO product, int brandId) {
        EntityAliasKey entityAliasKey = new EntityAliasKey(product.getId(), "PRODUCT", brandId);
        EntityAliasMappingData aliasMappingData = masterCache.getEntityAliasMappingData().get(entityAliasKey);
        if (aliasMappingData != null) {
            product = setAlias(product, aliasMappingData);
        }
        return product;
    }

    private ProductVO setAlias(ProductVO product, EntityAliasMappingData aliasMappingData) {
        ProductVO cloned = null;
        try {
            cloned = (ProductVO) product.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        if (aliasMappingData.getAlias() != null && aliasMappingData.getAlias().length() > 0) {
            cloned.setName(aliasMappingData.getAlias());
        }
        if (aliasMappingData.getDescription() != null && aliasMappingData.getDescription().length() > 0) {
            cloned.setDescription(aliasMappingData.getDescription());
        }

        return cloned;
    }

    public void cloneDayCloseDataForUnit(int newUnitId, int cloningUnitId) throws IOException {
        String endPoint = props.getSCMEndpoint() + SCMServiceEndpoints.CLONE_DAY_CLOSE_DATA;
        HttpResponse response=  WebServiceHelper.postRequestWithAuthInternal(endPoint,props.getSCMAuthInternal(),new IdCodeName(newUnitId,cloningUnitId+""));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addPaymentMethod(PaymentMode paymentMode) {
        PaymentMode mode = dao.addPaymentMethod(paymentMode);
        if(Objects.nonNull(mode)){
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<com.stpl.tech.master.domain.model.PaymentMode> getPaymentMethod() {
        List<com.stpl.tech.master.domain.model.PaymentMode> mode = dao.getPaymentMethod();
        if(Objects.nonNull(mode)){
            return mode;
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updatePaymentMethod(PaymentMode paymentMode) {
        try {
            PaymentMode mode = dao.getPaymentMethod(paymentMode);
            if (Objects.nonNull(mode)) {
                List<Denomination> denominations = dao.getDenomination(mode);
                mode.setDenominations(denominations);
                mode.setModeName(paymentMode.getModeName());
                mode.setModeType(paymentMode.getModeType());
                mode.setModeDescription(paymentMode.getModeDescription());
                mode.setSettlementType(paymentMode.getSettlementType());
                mode.setCommissionRate(paymentMode.getCommissionRate());
                mode.setModeStatus(paymentMode.getModeStatus());
                mode.setModeCategory(paymentMode.getModeCategory());
                mode.setAutomaticPullValidate(paymentMode.getAutomaticPullValidate());
                mode.setAutomaticCloseTransfer(paymentMode.getAutomaticCloseTransfer());
                mode.setEditable(paymentMode.getEditable());
                mode.setApplicableOnDiscountedOrders(paymentMode.getApplicableOnDiscountedOrders());
                mode.setNeedsSettlementSlip(paymentMode.getNeedsSettlementSlip());
                mode.setValidationSource(paymentMode.getValidationSource());
                mode.setLedgerName(paymentMode.getLedgerName());
                mode.setGeneratePull(paymentMode.isGeneratePull());
                mode.setAutomaticTransfer(paymentMode.getAutomaticTransfer());
                return dao.updatePaymentMode(mode);
            }
        } catch (Exception e) {
            LOG.error("Error while updating payment method for payment mode id : {}",paymentMode.getPaymentModeId(),e);
        }
        return false;
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updatePaymentModeMappingMethod(List<UnitPaymentModeMappingDetail> mapping){
        for(UnitPaymentModeMappingDetail modeMapping : mapping){
            dao.updatePaymentModeMappingMethod(modeMapping);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitPaymentModeMappingDetail> getPaymentModeMapping(Integer id) {
        List<UnitPaymentModeMappingDetail> mapping = dao.getPaymentModeMapping(id);
        if(Objects.nonNull(mapping) && !mapping.isEmpty()){
            LOG.error("Mapping found of size : {}",mapping.size());
        }else{
            LOG.error("Do not found any Mapping for id : {}",id);
        }
        return mapping;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setSuggestWalletStatus(CacheReferenceMetadata flag) {
        if (Objects.nonNull(flag) && Objects.nonNull(flag.getReferenceValue()) && Objects.nonNull(flag.getUpdatedBy())) {
            try {
                Optional<CacheReferenceMetadata> data = cacheReferenceMetadataDao.findByReferenceType(CacheReferenceType.DIRECT_PURCHASE_OF_GIFT_CARD.name());
                data.ifPresent(cacheReferenceMetadata -> {
                    cacheReferenceMetadata.setReferenceValue(flag.getReferenceValue());
                    cacheReferenceMetadata.setUpdatedBy(flag.getUpdatedBy());
                    cacheReferenceMetadataDao.save(cacheReferenceMetadata);
                });
                return true;
            } catch (Exception ex) {
                LOG.error("Error finding mapping for Activate Suggest Wallet");
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addPartnerEdcMapping(UnitToPartnerEdcMappingDetail unitToPartnerEdcMappingDetail){
        try {

                dao.addPartnerEdcMapping(unitToPartnerEdcMappingDetail);

            return true;
        }
        catch (Exception e)
        {
            LOG.error("Error While Adding Partner Edc mapping");
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitToPartnerEdcMapping> getPartnerEdcMappingList(String status) {

        return unitToPartnerEdcMappingDao.findAllByStatus(status);
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatePartnerEdcMapping(List<UnitToPartnerEdcMappingDetail> unitToPartnerEdcMappingDetails){
        try{
            for(UnitToPartnerEdcMappingDetail unitToPartnerEdcMappingDetail: unitToPartnerEdcMappingDetails){
                if(Objects.nonNull(unitToPartnerEdcMappingDetail.getId())){
                    dao.updatePartnerEdcMapping(unitToPartnerEdcMappingDetail);
                }
            }
            return true;
        }
        catch (Exception e)
        {
            LOG.error("Error While Updating Partner Edc mapping");
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateIpAddressForUnit(UnitIpAddressRequest unitIpAddressRequest) throws Exception {
        try {
            if (Objects.isNull(unitIpAddressRequest.getUnitId()) ||
                    Objects.isNull(unitIpAddressRequest.getAppName()) ||
                    Objects.isNull(unitIpAddressRequest.getTerminalId())) {
                return false;
            }

            UnitIpAddressData ipAddressData = unitIpAddressDao.findByUnitIdAndTerminalIdAndAppName(unitIpAddressRequest.getUnitId(),
                    unitIpAddressRequest.getTerminalId(), unitIpAddressRequest.getAppName());
            if (Objects.nonNull(ipAddressData)) {
                // does not have data yet
                ipAddressData.setIpAddress(unitIpAddressRequest.getIpAddress());
                ipAddressData.setLastUpdateTime(new Date());
                unitIpAddressDao.save(ipAddressData);
            } else {
                // update ip address
                ipAddressData = UnitIpAddressData.builder()
                        .unitId(unitIpAddressRequest.getUnitId())
                        .terminalId(unitIpAddressRequest.getTerminalId())
                        .lastUpdateTime(new Date())
                        .appName(unitIpAddressRequest.getAppName())
                        .ipAddress(unitIpAddressRequest.getIpAddress())
                        .build();
                unitIpAddressDao.save(ipAddressData);
            }

            //Update in cache
            Map<String, UnitIpAddressData> masterMap = masterCache.getUnitTerminalIpAddressMap();
            if (Objects.nonNull(masterMap)) {
                // no map for unit
                masterMap.put(generateIpAddressCacheKey(unitIpAddressRequest.getUnitId(),
                        unitIpAddressRequest.getTerminalId(), unitIpAddressRequest.getAppName()), ipAddressData);
            } else {
                throw new Exception("Unexpected error");
            }
            return true;
        } catch (Exception e) {
            LOG.error("Error while updating unit ip address data", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public UnitIpAddressData getIpAddressForUnit(UnitIpAddressRequest unitIpAddressRequest) {
        try {
            if (Objects.nonNull(unitIpAddressRequest.getUnitId())
                    && Objects.nonNull(unitIpAddressRequest.getTerminalId())
                    && Objects.nonNull(unitIpAddressRequest.getAppName())) {
                Map<String, UnitIpAddressData> map = masterCache.getUnitTerminalIpAddressMap();
                if (Objects.nonNull(map)) {
                    return map.get(generateIpAddressCacheKey(unitIpAddressRequest.getUnitId(),
                            unitIpAddressRequest.getTerminalId(), unitIpAddressRequest.getAppName()));
                }
            }
            return null;
        } catch (Exception e) {
            LOG.error("Exception while getting ip address", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String,List<String>> getAllActiveApplicationVersion(){
        try {
            Map<String, List<String>> versionMapping = new HashMap<>();
            List<ApplicationVersionDetail> versionDetailList = applicationVersionDao.findByStatus(AppConstants.ACTIVE);
            if(!CollectionUtils.isEmpty(versionDetailList)) {
                for (ApplicationVersionDetail data : versionDetailList) {
                    if (versionMapping.containsKey(data.getApplicationName())) {
                        versionMapping.get(data.getApplicationName()).add(data.getApplicationVersion());
                    } else {
                        List<String> list = new ArrayList<>();
                        list.add(data.getApplicationVersion());
                        versionMapping.put(data.getApplicationName(), list);
                    }
                }
            }
            return versionMapping;
        }catch (Exception e){
            LOG.info("Exception while getting application version Details");
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateCafePosVersion(String applicationName,String applicationVersion,Integer updatedBy,String releaseType,String buildName,String deploymentDescription) {
        try {
            if (Objects.nonNull(applicationVersion)) {
                ApplicationVersionDetail applicationVersionDetail = applicationVersionDao
                        .findByApplicationVersionAndApplicationName(applicationVersion, applicationName.toUpperCase());
                if (Objects.nonNull(applicationVersionDetail)) {
                    if (applicationVersionDetail.getStatus().equals(AppConstants.ACTIVE)) {
                        applicationVersionDetail.setStatus(AppConstants.IN_ACTIVE);
                        updateVersionCompatability(applicationName.toUpperCase(),applicationVersion,updatedBy,AppConstants.IN_ACTIVE);
                    } else {
                        applicationVersionDetail.setStatus(AppConstants.ACTIVE);
                        applicationVersionDetail.setBuildName(buildName);
                        applicationVersionDetail.setReleaseType(releaseType);
                        applicationVersionDetail.setDeploymentDescription(deploymentDescription);
                    }
                    applicationVersionDetail.setUpdatedBy(updatedBy);
                    applicationVersionDetail.setUpdatedTime(AppUtils.getCurrentTimestamp());
                    applicationVersionDao.save(applicationVersionDetail);
                }
                else{
                    applicationVersionDetail = ApplicationVersionDetail.builder()
                            .applicationName(applicationName)
                            .applicationVersion(applicationVersion)
                            .status(AppConstants.ACTIVE)
                            .updatedTime(AppUtils.getCurrentTimestamp())
                            .updatedBy(updatedBy)
                            .releaseType(releaseType)
                            .buildName(buildName)
                            .deploymentDescription(deploymentDescription)
                            .build();
                    applicationVersionDao.save(applicationVersionDetail);
                }
            }
        }catch(Exception e){
            LOG.info("Failed To update Versions : {}", e);
        }
    }




    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer,List<ApplicationVersionDetailData>> getUnitVersionMapping(){
        try {
            List<ApplicationVersionDetailData> versionDetailData = dao.getUnitVersionMapping();
            Map<Integer,List<ApplicationVersionDetailData>> data = new HashMap<>();
            if(Objects.nonNull(versionDetailData)) {
                for (ApplicationVersionDetailData versionData : versionDetailData) {
                    if (data.containsKey(versionData.getUnitId())) {
                        data.get(versionData.getUnitId()).add(versionData);
                    } else {
                        List<ApplicationVersionDetailData> detailData = new ArrayList<>();
                        detailData.add(versionData);
                        data.put(versionData.getUnitId(), detailData);
                    }
                }
            }
            return data;
        } catch (Exception e) {
            LOG.error("Exception while getting unit version mapping", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",propagation = Propagation.REQUIRED)
    public Integer createUpdateVersionEvent(List<VersionEventDomain> eventDomains){
        try {
            List<ApplicationVersionEvent> versionEvents = new ArrayList<>();
            Map<String,Set<Integer>> appUnits = new HashMap<>();
            if(Objects.nonNull(eventDomains)){
                for(VersionEventDomain domain : eventDomains) {
                    if(appUnits.containsKey(domain.getApplicationName())){
                        appUnits.get(domain.getApplicationName()).add(domain.getUnitId());
                    }
                    else {
                        Set<Integer> ids = new HashSet<>();
                        ids.add(domain.getUnitId());
                        appUnits.put(domain.getApplicationName(),ids);
                    }
                    Unit unit = cache.getUnit(domain.getUnitId());
                    if(Objects.nonNull(unit.getNoOfTerminals())) {
                        for (int i = 1; i < unit.getNoOfTerminals() + 1; i++) {
                            ApplicationVersionEvent event = new ApplicationVersionEvent();
                            event.setUnitId(domain.getUnitId());
                            event.setApplicationName(domain.getApplicationName());
                            event.setApplicationVersion(domain.getApplicationVersion());
                            event.setUpdatedBy(domain.getUpdatedBy());
                            event.setUpdatedTime(AppUtils.getCurrentTimestamp());
                            event.setStatus(EventStatusType.INITIATED.name());
                            event.setTerminalId(i);
                            event.setUnitRegion(domain.getUnitRegion());
                            versionEvents.add(event);
                        }
                    }
                }
                List<String> statuses = new ArrayList<>();
                statuses.add(EventStatusType.INITIATED.name());
                statuses.add(EventStatusType.NOTIFIED.name());
                for(Map.Entry<String,Set<Integer>> map : appUnits.entrySet()){
                    versionEventDao.updateExistingEvent(map.getValue(), EventStatusType.CANCELLED.name(),statuses,map.getKey());
                }
                versionEventDao.saveAll(versionEvents);
                return versionEvents.size();
            }
        } catch (Exception e) {
            LOG.error("Exception while creating events for version", e);
            return 0;
        }
        return 0;
    }

    @Override
    public ApkUploadResponse uploadApkBuild(MultipartFile file, String s3OfferBucket) {
        ApkUploadResponse apkUploadResponse = new ApkUploadResponse();
        try {
            String baseDir = "kettle-ui/cafe-app/apk_build";
            String fileName = file.getOriginalFilename();
            fileName = fileName.replaceAll(" ", "_").toLowerCase();
            LOG.info(":::::: Request to upload New App Build ::::::");
            FileDetail resultLocation = fileArchiveService.saveFileToS3(s3OfferBucket, baseDir, fileName, file, true);
            apkUploadResponse.setKey(resultLocation.getKey().replace("kettle-ui/",""));
            apkUploadResponse.setUpdated(true);
            return apkUploadResponse;
        } catch (FileArchiveServiceException e) {
            LOG.error("Encountered error while uploading APK Build to S3", e);
            apkUploadResponse.setKey(null);
            apkUploadResponse.setUpdated(false);
            return apkUploadResponse;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getApkBuild(HttpServletResponse response, String applicationName,
                            String applicationVersion) throws IOException {
        ApplicationVersionDetail applicationVersionDetail = applicationVersionDao.
                findByApplicationVersionAndApplicationName(applicationVersion,applicationName);
        String buildName = null;
        if(Objects.nonNull(applicationVersionDetail)){
            buildName = applicationVersionDetail.getBuildName();
        }
        return buildName;
    }

    private void setFileToResponse(HttpServletResponse response, File file) throws IOException {
        if (Objects.nonNull(file)) {
            response.setContentType("application/vnd.android.package-archive");
            String fileName = file.getName().substring(0,file.getName().length()-4);
            fileName = fileName.toUpperCase();
            response.addHeader("Content-Disposition", "attachment; filename=" + fileName+".apk");
            byte[] bytesArray = new byte[(int) file.length()];
            response.setContentLength(bytesArray.length);
            try {
                OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = new FileInputStream(file);
                int counter = 0;
                while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
                    outputStream.write(bytesArray, 0, counter);
                    outputStream.flush();
                }
                outputStream.close();
                inputStream.close();
            } catch (IOException e) {
                LOG.error("Encountered error while writing file to response stream", e);
                throw e;
            } finally {
                response.getOutputStream().flush();
                file.delete(); // delete the temporary file created after completing request
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<Integer,List<ApplicationVersionEvent>> getAllUnitVersionEvent(){
        try {
            List<ApplicationVersionEvent> versionEvents = versionEventDao.findByStatus(EventStatusType.INITIATED.name());
            if(!CollectionUtils.isEmpty(versionEvents)){
                Map<Integer,List<ApplicationVersionEvent>> data = new HashMap<>();
                for(ApplicationVersionEvent event : versionEvents){
                    if(data.containsKey(event.getUnitId())){
                        data.get(event.getUnitId()).add(event);
                    }
                    else{
                        List<ApplicationVersionEvent> appEvent = new ArrayList<>();
                        appEvent.add(event);
                        data.put(event.getUnitId(),appEvent);
                    }
                }
                return data;
            }
        }catch (Exception e){
            LOG.info("Error while getting all unit version events {}",e);
        }
        return new HashMap<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addVersionCompatability(String applicationName, String applicationVersion, String posVersion, Integer updatedBy){
        List<VersionCompatibilityData> resultList = new ArrayList<>();
        VersionCompatibilityData data = versionCompatibilityDao.findByApplicationNameAndApplicationVersionAndStatus(applicationName,applicationVersion,AppConstants.ACTIVE);
        if(Objects.nonNull(data)){
            data.setStatus(AppConstants.IN_ACTIVE);
            data.setUpdatedBy(updatedBy);
            data.setUpdatedAt(AppUtils.getCurrentTimestamp());
            resultList.add(data);
        }
        VersionCompatibilityData versionCompatibilityData = VersionCompatibilityData.builder()
                        .applicationName(applicationName)
                        .applicationVersion(applicationVersion)
                        .posVersion(posVersion)
                        .status(AppConstants.ACTIVE)
                        .updatedBy(updatedBy)
                        .updatedAt(AppUtils.getCurrentTimestamp())
                        .build();
                resultList.add(versionCompatibilityData);
        versionCompatibilityDao.saveAll(resultList);
    }

    private void updateVersionCompatability(String applicationName,String applicationVersion,Integer updatedBy,String status){
        if(applicationName.equals("POS")){
            List<VersionCompatibilityData> versionData = versionCompatibilityDao.findByPosVersion(applicationVersion);
            if(!CollectionUtils.isEmpty(versionData)) {
                for (VersionCompatibilityData data : versionData) {
                    if (!data.getStatus().equals(status)) {
                        data.setStatus(status);
                        data.setUpdatedBy(updatedBy);
                        data.setUpdatedAt(AppUtils.getCurrentTimestamp());
                    }
                }
                versionCompatibilityDao.saveAll(versionData);
            }
        }
        else{
            List<VersionCompatibilityData> versionData = versionCompatibilityDao.findByApplicationNameAndApplicationVersionOrderByUpdatedAtDesc(applicationName,applicationVersion);
            if(!CollectionUtils.isEmpty(versionData)) {
                for (VersionCompatibilityData data : versionData) {
                    if (!data.getStatus().equals(status)) {
                        data.setStatus(status);
                        data.setUpdatedBy(updatedBy);
                        data.setUpdatedAt(AppUtils.getCurrentTimestamp());
                    }
                }
                versionCompatibilityDao.saveAll(versionData);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public 	List<String> getCompatibleApplicationVersion(String applicationName,String posVersion){
        List<String> compatibleVersions = new ArrayList<>();
        List<VersionCompatibilityData> versionCompatibilityData = versionCompatibilityDao.findByApplicationNameAndPosVersionAndStatus(applicationName,posVersion,AppConstants.ACTIVE);
        if(!CollectionUtils.isEmpty(versionCompatibilityData)) {
            for (VersionCompatibilityData data : versionCompatibilityData) {
                compatibleVersions.add(data.getApplicationVersion());
            }
        }
        return compatibleVersions;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public Map<String,UnitVersionDetail> getUnitVersions(UnitVersionDomain unitVersionDomain) {
        Map<String,UnitVersionDetail> unitVersionDetail = new HashMap<>();
        List<ApplicationVersionEvent> currentEvents = getUnitCurrentEventVersion(unitVersionDomain.getUnitId(), unitVersionDomain.getTerminalId(), unitVersionDomain.getApplicationName());
        if (!CollectionUtils.isEmpty(currentEvents)) {
            setExpectedVersions(currentEvents,unitVersionDetail,unitVersionDomain);
        }
        List<ApplicationVersionDetailData> currentVersions = getCurrentUnitApplicationVersion(unitVersionDomain.getUnitId(), unitVersionDomain.getTerminalId(), unitVersionDomain.getApplicationName());
        if (!CollectionUtils.isEmpty(currentVersions)) {
            setCurrentVersions(currentVersions,unitVersionDetail);
        }
        List<ApplicationVersionDomian> versionDomains = new ArrayList<>();
        for(String appName : unitVersionDomain.getApplicationName()){
            if(Objects.nonNull(unitVersionDetail.get(appName))){
                UnitVersionDetail detail = unitVersionDetail.get(appName);
                if(Objects.nonNull(detail.getExpectedVersion())){
                    ApplicationVersionDomian value = new ApplicationVersionDomian(appName,detail.getExpectedVersion());
                    versionDomains.add(value);
                }
            }
        }
        if(!CollectionUtils.isEmpty(versionDomains)){
            List<ApplicationVersionDetail> applicationVersionDetails = getApplicationVersionDetail(versionDomains);
            if(!CollectionUtils.isEmpty(applicationVersionDetails)) {
                for (ApplicationVersionDetail versionDetail : applicationVersionDetails) {
                    if (Objects.nonNull(unitVersionDetail.get(versionDetail.getApplicationName())) && versionDetail.getApplicationVersion().equals(unitVersionDetail.get(versionDetail.getApplicationName()).getExpectedVersion())) {
                        unitVersionDetail.get(versionDetail.getApplicationName()).setExpectedVersionDescription(versionDetail.getDeploymentDescription());
                        unitVersionDetail.get(versionDetail.getApplicationName()).setExpectedVersionReleaseType(versionDetail.getReleaseType());
                        unitVersionDetail.get(versionDetail.getApplicationName()).setExpectedVersionDate(versionDetail.getUpdatedTime());
                        if(Objects.nonNull(versionDetail.getBuildName())) {
                            unitVersionDetail.get(versionDetail.getApplicationName()).setExpectedVersionBuildName(versionDetail.getBuildName());
                        }
                    }
                }
            }
        }
        if(Objects.nonNull(unitVersionDetail)) {
            for (String appName : unitVersionDomain.getApplicationName()) {
                if (Objects.nonNull(unitVersionDetail.get(appName))) {
                    if (Objects.isNull(unitVersionDetail.get(appName).getCurrentVersion())) {
                        unitVersionDetail.get(appName).setCurrentVersion("2.0.0");
                    }
                }
            }
        }
        if(unitVersionDomain.getRequestSource().equals(AppConstants.CAFE_APP)) {
            boolean hasCurrentCompatibleEvents = getUnitCurrentCompatiblePosVersion(unitVersionDomain.getUnitId(), unitVersionDomain.getTerminalId(), AppConstants.CAFE_APP, unitVersionDomain.getCafeAppVersion(), Arrays.asList(EventStatusType.INITIATED.name(),EventStatusType.DEPLOYED.name()));
            if (hasCurrentCompatibleEvents) {
                List<String> versions = getCompatiblePosVersion(AppConstants.CAFE_APP, unitVersionDomain.getCafeAppVersion());
                if(!CollectionUtils.isEmpty(versions) && versions.size()>0) {
                    unitVersionDetail.get(AppConstants.POS).setCurrentVersion(versions.get(0));
                    unitVersionDetail.get(AppConstants.CAFE_APP).setCurrentVersion(unitVersionDomain.getCafeAppVersion());
                }
                else{
                    unitVersionDetail.get(AppConstants.POS).setCurrentVersion(null);
                    unitVersionDetail.get(AppConstants.CAFE_APP).setCurrentVersion(unitVersionDomain.getCafeAppVersion());
                }
            }
            else {
                unitVersionDetail.get(AppConstants.POS).setCurrentVersion(null);
                unitVersionDetail.get(AppConstants.CAFE_APP).setCurrentVersion(unitVersionDomain.getCafeAppVersion());
            }
        }

        // Handle other apps like TABLE_SERVICE OR MONK_APP
        if(Arrays.stream(Application.values()).anyMatch(application -> application.name().equals(unitVersionDomain.getRequestSource()))) {
            if(Objects.isNull(unitVersionDetail.get(unitVersionDomain.getRequestSource()))) {
                UnitVersionDetail detail = new UnitVersionDetail();
                unitVersionDetail.put(unitVersionDomain.getRequestSource(), detail);
            }
            unitVersionDetail.get(unitVersionDomain.getRequestSource()).setCurrentVersion(unitVersionDomain.getApplicationVersion());
        }
        return unitVersionDetail;
    }

    private void setExpectedVersions(List<ApplicationVersionEvent> currentEvents,Map<String,UnitVersionDetail> unitVersionDetail,UnitVersionDomain domain){
        for (ApplicationVersionEvent event : currentEvents) {
            UnitVersionDetail detail = new UnitVersionDetail();
            detail.setExpectedVersionDate(event.getUpdatedTime());
            unitVersionDetail.put(event.getApplicationName(), detail);
            if (AppConstants.POS.equals(event.getApplicationName()) && event.getApplicationVersion().equals(domain.getPosVersion())
                    && !EventStatusType.DEPLOYED.name().equals(event.getStatus()) && AppConstants.POS.equals(domain.getRequestSource())) {
                updateVersionEventStatus(domain.getUnitId(), domain.getTerminalId(), event.getApplicationName(), domain.getPosVersion());
                unitVersionDetail.get(AppConstants.POS).setExpectedVersion(domain.getPosVersion());
            }
            else if (AppConstants.CAFE_APP.equals(event.getApplicationName()) && event.getApplicationVersion().equals(domain.getCafeAppVersion())
                    && !EventStatusType.DEPLOYED.name().equals(event.getStatus()) && AppConstants.POS.equals(domain.getRequestSource())) {
                updateVersionEventStatus(domain.getUnitId(), domain.getTerminalId(), event.getApplicationName(), domain.getCafeAppVersion());
                unitVersionDetail.get(AppConstants.CAFE_APP).setExpectedVersion(domain.getCafeAppVersion());
            } else if (Arrays.stream(Application.values()).anyMatch(application -> application.name().equals(event.getApplicationName())) &&
                    event.getApplicationVersion().equals(domain.getApplicationVersion()) && !EventStatusType.DEPLOYED.name().equals(event.getStatus())) {
                updateVersionEventStatus(domain.getUnitId(), domain.getTerminalId(), event.getApplicationName(), domain.getApplicationVersion());
                unitVersionDetail.get(event.getApplicationName()).setExpectedVersion(domain.getApplicationVersion());
            }

            else {
                if(AppConstants.CAFE_APP.equals(event.getApplicationName())) {
                    unitVersionDetail.get(AppConstants.CAFE_APP).setExpectedVersion(event.getApplicationVersion());
                }
                if(AppConstants.POS.equals(event.getApplicationName())) {
                    unitVersionDetail.get(AppConstants.POS).setExpectedVersion(event.getApplicationVersion());
                }
                if(!AppConstants.POS.equals(event.getApplicationName()) && !AppConstants.CAFE_APP.equals(event.getApplicationName())
                    && Arrays.stream(Application.values()).anyMatch(application -> application.name().equals(event.getApplicationName()))) {
                    unitVersionDetail.get(event.getApplicationName()).setExpectedVersion(event.getApplicationVersion());
                }
            }
        }
    }

    private void setCurrentVersions(List<ApplicationVersionDetailData> currentVersions,Map<String,UnitVersionDetail> unitVersionDetail){
        for (ApplicationVersionDetailData current : currentVersions) {
            if (Objects.nonNull(unitVersionDetail.get(current.getApplicationName()))) {
                unitVersionDetail.get(current.getApplicationName()).setCurrentVersion(current.getApplicationVersion());
                unitVersionDetail.get(current.getApplicationName()).setExpectedVersionDate(current.getLastUpdateTime());
            } else {
                UnitVersionDetail detail = new UnitVersionDetail();
                detail.setCurrentVersion(current.getApplicationVersion());
                detail.setExpectedVersion(current.getApplicationVersion());
                detail.setExpectedVersionDate(current.getLastUpdateTime());
                unitVersionDetail.put(current.getApplicationName(),detail);
            }
        }
    }

    private List<ApplicationVersionDetailData> getCurrentUnitApplicationVersion(Integer unitId, Integer terminalId, List<String> applicationName){
        try {
            List<ApplicationVersionDetailData> currentVersions = applicationVersionDetailDataDao.findByUnitIdAndTerminalAndApplicationNameInAndVersionStatus(unitId,terminalId,applicationName,AppConstants.ACTIVE);
            if(!CollectionUtils.isEmpty(currentVersions)){
                return currentVersions;
            }
        }catch (Exception e){
            LOG.error("Error while getting unit current version data ::::: ",e);
        }
        return null;
    }

    private List<ApplicationVersionEvent> getUnitCurrentEventVersion(Integer unitId, Integer terminalId, List<String> applicationName){
        try {
            List<ApplicationVersionEvent> currentEvents = versionEventDao.findByUnitIdAndTerminalIdAndApplicationNameInAndStatus(unitId,terminalId,applicationName,EventStatusType.INITIATED.name());
            if(!CollectionUtils.isEmpty(currentEvents)){
                return currentEvents;
            }
        }catch (Exception e){
            LOG.error("Error while getting unit current event data ::::: ",e);
        }
        return null;
    }

    public void updateVersionEventStatus(Integer unitId, Integer terminalId, String applicationName, String applicationVersion) {
        try {
            versionEventDao.updateVersionEventStatus(unitId, terminalId, applicationName, EventStatusType.DEPLOYED.name());
            applicationVersionDetailDataDao.updateUnitVersionStatus(unitId, terminalId, applicationName, AppConstants.IN_ACTIVE);
            ApplicationVersionDetailData data = new ApplicationVersionDetailData();
            data.setUnitId(unitId);
            data.setTerminal(terminalId);
            data.setApplicationName(applicationName);
            data.setApplicationVersion(applicationVersion);
            data.setLastUpdateTime(AppUtils.getCurrentTimestamp());
            data.setUnitRegion(cache.getUnit(unitId).getRegion());
            data.setVersionStatus(AppConstants.ACTIVE);
            applicationVersionDetailDataDao.save(data);
        } catch (Exception e) {
            LOG.error("Error while updating the version event of the unit with id {}", unitId, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ApplicationVersionDetail> getApplicationVersionDetail(List<ApplicationVersionDomian> applicationVersionList){
        List<ApplicationVersionDetail> applicationVersionDetail = new ArrayList<>();
        try {
            for(ApplicationVersionDomian data : applicationVersionList){
                ApplicationVersionDetail result = applicationVersionDao.findByApplicationVersionAndApplicationName(data.getApplicationVersion(),data.getApplicationName());
                if (Objects.nonNull(result) && AppConstants.ACTIVE.equals(result.getStatus())) {
                    applicationVersionDetail.add(result);
                }
            }
            return applicationVersionDetail;
        }catch (Exception e){
            LOG.info("Error in fetching Application VersionDetail");
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<String> getCompatiblePosVersion(String applicationName,String applicationVersion){
        List<String> result = new ArrayList<>();
        try {
            List<VersionCompatibilityData> versionCompatibilityDataList = versionCompatibilityDao.findByApplicationNameAndApplicationVersionOrderByUpdatedAtDesc(applicationName,applicationVersion);
            if(Objects.nonNull(versionCompatibilityDataList)) {
                for (VersionCompatibilityData data : versionCompatibilityDataList) {
                    if (data.getStatus().equals(AppConstants.ACTIVE)) {
                        result.add(data.getPosVersion());
                    }
                }
            }
            return result;
        }catch (Exception e){
            LOG.info("Error in Fetching Version Compatibility Details : {}",e);
            return result;
        }
    }

    private boolean getUnitCurrentCompatiblePosVersion(Integer unitId, Integer terminalId,String applicationName,String cafeAppVersion,List<String> statuses){
        try {
            List<ApplicationVersionEvent> currentEvents = versionEventDao.findByUnitIdAndTerminalIdAndApplicationNameAndApplicationVersionAndStatusIn(unitId,terminalId,applicationName,cafeAppVersion, statuses);
            if(!CollectionUtils.isEmpty(currentEvents)){
                return true;
            }
        }catch (Exception e){
            LOG.error("Error while getting unit current event data ::::: ",e);
            return false;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateFeedbackQuestions(FeedbackQuestionDomain domain){
        FeedbackQuestionsDetail feedbackQuestionsDetail = new FeedbackQuestionsDetail();
        try {
            if(Objects.nonNull(domain) && Objects.isNull(domain.getQuestionId())) {
                feedbackQuestionsDetail.setQuestion(domain.getQuestion());
                feedbackQuestionsDetail.setQuestionType(domain.getQuestionType());
                feedbackQuestionsDetail.setQuestionStatus(AppConstants.ACTIVE);
                feedbackQuestionsDetail.setUpdatedBy(domain.getUpdatedBy());
                feedbackQuestionsDetail.setUpdatedAt(AppUtils.getCurrentTimestamp());
                feedbackQuestionsDetail.setQuestionReason(domain.getQuestionReason());
                feedBackQuestionDetailDao.save(feedbackQuestionsDetail);
                return true;
            }else if(Objects.nonNull(domain)){
                feedbackQuestionsDetail = feedBackQuestionDetailDao.findById(domain.getQuestionId()).orElse(null);
                if(Objects.nonNull(feedbackQuestionsDetail)){
                    feedbackQuestionsDetail.setQuestion(domain.getQuestion());
                    feedbackQuestionsDetail.setQuestionType(domain.getQuestionType());
                    feedbackQuestionsDetail.setUpdatedBy(domain.getUpdatedBy());
                    feedbackQuestionsDetail.setUpdatedAt(AppUtils.getCurrentTimestamp());
                    feedbackQuestionsDetail.setQuestionReason(domain.getQuestionReason());
                    feedBackQuestionDetailDao.save(feedbackQuestionsDetail);
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            LOG.error("Error in updating Feedback Questions",e);
            return false;
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private void updateFeedbackMapping(Integer questionId,Integer updatedBy,String status){
        String resultStatus = "";
        if(AppConstants.ACTIVE.equals(status)){
            resultStatus = AppConstants.IN_ACTIVE;
        }else{
            resultStatus = AppConstants.ACTIVE;
        }
        try {
            List<FeedbackQuestionsUnitMapping> data = feedBackQuestionsUnitMappingDao.findByQuestionIdAndMappingStatus(questionId,resultStatus);
            List<FeedbackQuestionsUnitMapping> result = new ArrayList<>();
            if(Objects.nonNull(data) && !data.isEmpty()){
                for(FeedbackQuestionsUnitMapping mapping : data){
                    mapping.setMappingStatus(status);
                    mapping.setUpdatedBy(updatedBy);
                    mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());
                    result.add(mapping);
                }
                feedBackQuestionsUnitMappingDao.saveAll(result);
            }
        }catch (Exception e){
            LOG.error("Error in inactivating mapped unit",e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<FeedbackQuestionsDetail> getAllFeedBackQuestion(String questionType){
        return feedBackQuestionDetailDao.findByQuestionType(questionType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deleteFeedBackQuestion(Integer questionId){
        try {
            feedBackQuestionDetailDao.deleteById(questionId);
            return true;
        }catch (Exception e){
            LOG.error("Error in deleting Feedback detail",e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addFeedbackUnitMapping(FeedbackQuesMappingDomain request){
        List<FeedbackQuestionsUnitMapping> feedbackQuestionsUnitMappingList = new ArrayList<>();
        try {
            if(Objects.nonNull(request) && Objects.nonNull(request.getQuestionId()) &&
                    (Objects.nonNull(request.getUnitName()) && !request.getUnitName().isEmpty())){
                for(String unit : request.getUnitName()){
                    FeedbackQuestionsUnitMapping data = new FeedbackQuestionsUnitMapping();
                    data.setUnitName(unit);
                    data.setQuestionId(request.getQuestionId());
                    data.setQuestionType(request.getQuestionType());
                    data.setMappingStatus(AppConstants.ACTIVE);
                    data.setUpdatedAt(AppUtils.getCurrentTimestamp());
                    data.setUpdatedBy(request.getUpdateBy());
                    feedbackQuestionsUnitMappingList.add(data);
                }
                feedBackQuestionsUnitMappingDao.saveAll(feedbackQuestionsUnitMappingList);
                return true;
            }else {
                return false;
            }
        }catch (Exception e){
            LOG.error("Error in add unit mapping for feedback question", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateFeedbackUnitMapping(Integer mappingId,Integer updatedBy){
        FeedbackQuestionsUnitMapping data = new FeedbackQuestionsUnitMapping();
        try{
            data = feedBackQuestionsUnitMappingDao.findById(mappingId).orElse(null);
            if(Objects.nonNull(data)){
                if(data.getMappingStatus().equals(AppConstants.ACTIVE)){
                    data.setMappingStatus(AppConstants.IN_ACTIVE);
                    data.setUpdatedBy(updatedBy);
                    data.setUpdatedAt(AppUtils.getCurrentTimestamp());
                }else {
                    data.setMappingStatus(AppConstants.ACTIVE);
                    data.setUpdatedBy(updatedBy);
                    data.setUpdatedAt(AppUtils.getCurrentTimestamp());
                }
                feedBackQuestionsUnitMappingDao.save(data);
                return true;
            }
        }catch (Exception e){
            LOG.error("Error in updating feedback unit mapping",e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<FeedbackQuestionsUnitMapping> getAllFeedbackQuestionUnitMapping(Integer questionId){
        try {
            return feedBackQuestionsUnitMappingDao.findByQuestionId(questionId);
        }catch (Exception e){
            LOG.error("Error in fetching mapping for feedbackQuestions",e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean changeQuestionStatus(Integer questionId,Integer updatedBy){
        try{
            FeedbackQuestionsDetail feedbackQuestionsDetail = null;
            feedbackQuestionsDetail = feedBackQuestionDetailDao.findById(questionId).orElse(null);
            if(Objects.nonNull(feedbackQuestionsDetail)){
                if(AppConstants.ACTIVE.equals(feedbackQuestionsDetail.getQuestionStatus())) {
                    feedbackQuestionsDetail.setQuestionStatus(AppConstants.IN_ACTIVE);
                    updateFeedbackMapping(questionId,updatedBy,AppConstants.IN_ACTIVE);
                }else {
                    feedbackQuestionsDetail.setQuestionStatus(AppConstants.ACTIVE);
                    updateFeedbackMapping(questionId,updatedBy,AppConstants.ACTIVE);
                }
                feedbackQuestionsDetail.setUpdatedBy(updatedBy);
                feedbackQuestionsDetail.setUpdatedAt(AppUtils.getCurrentTimestamp());
                feedBackQuestionDetailDao.save(feedbackQuestionsDetail);
                return true;
            }
            return false;
        }catch (Exception e){
            LOG.error("Error in changing status of question",e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean bulkUpdateFeedbackUnitMapping(FeedbackQuesMappingDomain domain){
        List<Integer> mappingIds = new ArrayList<>();
        if(Objects.nonNull(domain) && Objects.nonNull(domain.getMappingIds())){
            mappingIds = domain.getMappingIds();
        }
        if(!mappingIds.isEmpty()){
            List<FeedbackQuestionsUnitMapping> mappingList = new ArrayList<>();
            List<FeedbackQuestionsUnitMapping> resultList = new ArrayList<>();
            try{
                mappingList = feedBackQuestionsUnitMappingDao.findAllById(mappingIds);
                if(Objects.nonNull(mappingList) && !mappingList.isEmpty()){
                    for(FeedbackQuestionsUnitMapping data : mappingList){
                        if(data.getMappingStatus().equals(AppConstants.ACTIVE)){
                            data.setMappingStatus(AppConstants.IN_ACTIVE);
                        }else {
                            data.setMappingStatus(AppConstants.ACTIVE);
                        }
                        data.setUpdatedBy(domain.getUpdateBy());
                        data.setUpdatedAt(AppUtils.getCurrentTimestamp());
                        resultList.add(data);
                    }
                    feedBackQuestionsUnitMappingDao.saveAll(resultList);
                    return true;
                }
            }catch (Exception e){
                LOG.error("Error in Changing status of mapping");
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM" , readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addUnitContacts(UnitContactDetails unitContactDetails) {
        try {

            UnitContactDetailsData unitContactDetailsData = new UnitContactDetailsData();
            unitContactDetailsData.setUnitId(unitContactDetails.getUnitId());
            unitContactDetailsData.setUnitName(unitContactDetails.getUnitName());
            List<IdCodeName> contactDetails = unitContactDetails.getContactDetails();

            UnitContactDetailsData existingUnitContactDetailsData = unitContactDetailsDao.findByUnitIdAndStatus(unitContactDetails.getUnitId(), AppConstants.ACTIVE);
            if (Objects.nonNull(existingUnitContactDetailsData)) {
                existingUnitContactDetailsData.setStatus(AppConstants.IN_ACTIVE);
                unitContactDetailsDao.save(existingUnitContactDetailsData);
            }

            if (!CollectionUtils.isEmpty(contactDetails)) {
                boolean contactDetailsAdded = false;
                if (contactDetails.size() > 0 && !StringUtils.isEmpty(contactDetails.get(0).getName()) && !StringUtils.isEmpty(contactDetails.get(0).getCode())) {
                    unitContactDetailsData.setFirstContactName(contactDetails.get(0).getName());
                    unitContactDetailsData.setFirstContactNumber(contactDetails.get(0).getCode());
                    contactDetailsAdded = true;
                }
                if (contactDetails.size() > 1 && !StringUtils.isEmpty(contactDetails.get(1).getName()) && !StringUtils.isEmpty(contactDetails.get(1).getCode())) {
                    unitContactDetailsData.setSecondContactName(contactDetails.get(1).getName());
                    unitContactDetailsData.setSecondContactNumber(contactDetails.get(1).getCode());
                    contactDetailsAdded = true;
                }
                if (contactDetails.size() > 2 && !StringUtils.isEmpty(contactDetails.get(2).getName()) && !StringUtils.isEmpty(contactDetails.get(2).getCode())) {
                    unitContactDetailsData.setThirdContactName(contactDetails.get(2).getName());
                    unitContactDetailsData.setThirdContactNumber(contactDetails.get(2).getCode());
                    contactDetailsAdded = true;
                }
                if (contactDetailsAdded) {

                    unitContactDetailsData.setStatus(AppConstants.ACTIVE);
                    unitContactDetailsDao.save(unitContactDetailsData);
                    cache.refreshSingleUnitContacts(unitContactDetailsData);
                    LOG.info("Contacts details Saved for unitId: {}", unitContactDetails.getUnitId());
                    return true;
                }
                LOG.info("No New Contacts Exist So not Updated");
            }
            return false;
        } catch (Exception e) {
            LOG.error("Error in Saving ContactDetails of unitId: {}", unitContactDetails.getContactDetails());
            return false;
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM" , readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateUnit(final int unit, final Integer userId) throws DataNotFoundException, DataUpdationException, IOException {
        LOG.info("Deactivating unit with unit Id {}", unit);
        Unit oldUnit = masterService.getUnit(unit, false);
        UnitStatusData oldData = new UnitStatusData(oldUnit.getId(), oldUnit.getStatus().name(), oldUnit.isLive());
        UnitStatusData newData = new UnitStatusData(unit, UnitStatus.IN_ACTIVE.name(), false);
        changeUnitStatus(unit, UnitStatus.IN_ACTIVE);
        changeUnitLiveStatus(unit, false);
        if (oldUnit.getFamily().equals(UnitCategory.CAFE)) {
            changeUnitStatusForDineInAndChaayos(unit, false, false);
        }
        loggerService.addActivity(oldData, newData, userId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitToPartnerDqrMapping> getPartnerDqrMappingList(String status) {
        if((AppConstants.ACTIVE + "_" + AppConstants.IN_ACTIVE).equals(status)){
            return unitToPartnerDqrMappingDao.findAll();
        }
        return unitToPartnerDqrMappingDao.findAllByStatus(status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addPartnerDqrMapping(UnitToPartnerDqrMapping unitToPartnerDqrMapping){
        try {
            Optional<UnitToPartnerDqrMapping> mapping = unitToPartnerDqrMappingDao.findByUnitId(unitToPartnerDqrMapping.getUnitId());
            if(mapping.isPresent()){
                mapping.get().setStatus(AppConstants.IN_ACTIVE);
                unitToPartnerDqrMappingDao.save(mapping.get());
            }
            unitToPartnerDqrMappingDao.save(unitToPartnerDqrMapping);
            return true;
        }
        catch (Exception e) {
            LOG.error("Error While Adding Partner Dqr mapping");
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatePartnerDqrMapping(List<UnitToPartnerDqrMapping> unitToPartnerDqrMappings){
        try{
            unitToPartnerDqrMappingDao.saveAll(unitToPartnerDqrMappings);
            return true;
        }
        catch (Exception e)
        {
            LOG.error("Error While Updating Partner Dqr mapping",e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public UnitWsToStationCategoryMappingRequest getUnitWsToStationCategoryMapping(Integer unitId) {
        UnitWsToStationCategoryMappingRequest response = new UnitWsToStationCategoryMappingRequest();
        try {
            List<UnitWSToStationCategoryMappingData> activeMappings = unitWSToStationCategoryMappingDao.findByUnitIdAndMappingStatus(unitId, AppConstants.ACTIVE);
            if (Objects.nonNull(activeMappings) && !activeMappings.isEmpty()) {
                response.setWsToStationCategoryMappings(activeMappings.stream().collect(Collectors.toMap(UnitWSToStationCategoryMappingData::getStationCategory,
                        UnitWSToStationCategoryMappingData::getWorkStationName, (e1, e2) -> e1, LinkedHashMap::new)));
            }
        } catch (Exception e) {
            LOG.error("Error While getUnitWsToStationCategoryMapping :: for Unit Id : {} ", unitId, e);
        }
        return response;
    }

    @Override
    public Set<String> getAllStations() {
        return masterCache.getWorkStationsStationCategories();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addUnitWsToStationCategoryMapping(UnitWsToStationCategoryMappingRequest wsToStationCategoryMapping) {
        try {
            List<UnitWSToStationCategoryMappingData> activeMappings = unitWSToStationCategoryMappingDao.findByUnitIdAndMappingStatus(wsToStationCategoryMapping.getUnitId(), AppConstants.ACTIVE);
            if (Objects.nonNull(activeMappings) && !activeMappings.isEmpty()) {
                for (UnitWSToStationCategoryMappingData activeMapping : activeMappings) {
                    activeMapping.setMappingStatus(AppConstants.IN_ACTIVE);
                }
                unitWSToStationCategoryMappingDao.saveAll(activeMappings);
            }
            List<UnitWSToStationCategoryMappingData> newMappings = new ArrayList<>();
            for (Map.Entry<String, String> mapEntry : wsToStationCategoryMapping.getWsToStationCategoryMappings().entrySet()) {
                UnitWSToStationCategoryMappingData wsMapping = new UnitWSToStationCategoryMappingData();
                wsMapping.setUnitId(wsToStationCategoryMapping.getUnitId());
                wsMapping.setWorkStationName(mapEntry.getValue());
                wsMapping.setStationCategory(mapEntry.getKey());
                wsMapping.setCreatedBy(wsToStationCategoryMapping.getCreatedBy());
                wsMapping.setCreationTime(AppUtils.getCurrentTimestamp());
                wsMapping.setMappingStatus(AppConstants.ACTIVE);
                newMappings.add(wsMapping);
            }
            unitWSToStationCategoryMappingDao.saveAllAndFlush(newMappings);
            return true;
        } catch (Exception e) {
            LOG.error("Error While addUnitWsToStationCategoryMapping :: for Unit Id : {} ", wsToStationCategoryMapping.getUnitId(), e);
        }
        return false;
    }

    @Override
    public Map<String, Set<Pair<Integer, String>>> getLocationByZone() {
        Map<String, Set<Pair<Integer, String>>> locationByZone = new HashMap<>();
        Set<Pair<Integer, String>> uniqueLocations = new HashSet<>();

        for (Unit unit : masterCache.getUnits().values()) {
            if (!unit.getStatus().equals(UnitStatus.ACTIVE)) {
                continue;
            }
            Pair<Integer, String> locationPair =new Pair<>(unit.getLocation().getId(), unit.getLocation().getName());
            if (!uniqueLocations.contains(locationPair)) {
                if(locationByZone.containsKey(unit.getUnitZone())) {
                    locationByZone.get(unit.getUnitZone()).add(locationPair);
                } else {
                    locationByZone.put(unit.getUnitZone(), new HashSet<>(Arrays.asList(locationPair)));
                }
                uniqueLocations.add(locationPair);
            }
        }

        return locationByZone;
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<UnitBasicDetail> getAllTestingUnits(String category) {
        List<UnitBasicDetail> units = new ArrayList<>();
        for(UnitBasicDetail unit : masterCache.getAllUnits()) {
            if(Boolean.TRUE.equals(unit.getIsTestingUnit())) {
                units.add(unit);
            }
        }
        return units;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean syncUnitProductPricing(Integer fromUnitId, Integer toUnitId, Integer loggedInUser) {
        Map<Integer, Map<Integer, UnitProductMapping>> mappingForUnits= dao.getUnitProductMappingForUnits(List.of(fromUnitId, toUnitId));
        Map<Integer, UnitProductMapping> fromUnitMappings = Objects.requireNonNullElse(mappingForUnits.get(fromUnitId), new HashMap<>());
        Map<Integer, UnitProductMapping> toUnitMappings = Objects.requireNonNullElse(mappingForUnits.get(toUnitId), new HashMap<>());
        UnitDetail toUnitDetail = dao.findById(toUnitId);

        List<UnitProductMapping> changedUnitProductMappings = new ArrayList<>();
        int uppUpdatedCount = 0;

        // check UPM based on toUnitMapping
        for(UnitProductMapping mapping : toUnitMappings.values()) {
            uppUpdatedCount += syncUnitsProductsMapping(mapping, fromUnitMappings.get(mapping.getProductDetail().getProductId()), changedUnitProductMappings, loggedInUser);
            fromUnitMappings.remove(mapping.getProductDetail().getProductId());
        }
        int upmUpdatedCount = changedUnitProductMappings.size();
        dao.updateUnitProductMapping(changedUnitProductMappings);
        changedUnitProductMappings.clear();

        // check UPM based on fromUnitMapping
        for(UnitProductMapping mapping : fromUnitMappings.values()) {
            createNewUnitProductMapping(toUnitMappings.get(mapping.getProductDetail().getProductId()), mapping, changedUnitProductMappings, toUnitDetail, loggedInUser);
        }
        upmUpdatedCount = upmUpdatedCount + changedUnitProductMappings.size();
        changedUnitProductMappings = dao.updateUnitProductMapping(changedUnitProductMappings);

        // add created mapping into toUnitMap
        for(UnitProductMapping upm : changedUnitProductMappings) {
            toUnitMappings.put(upm.getProductDetail().getProductId(), upm);
        }

        for(UnitProductMapping mapping : fromUnitMappings.values()) {
            uppUpdatedCount += mapUnitProductPricing(toUnitMappings.get(mapping.getProductDetail().getProductId()).getUnitProductPricings(), mapping.getUnitProductPricings(), loggedInUser, toUnitMappings.get(mapping.getProductDetail().getProductId()));
        }
        addNewSyncLog(upmUpdatedCount, uppUpdatedCount, toUnitDetail, dao.findById(fromUnitId), loggedInUser);
        return true;
    }

    private Integer syncUnitsProductsMapping(UnitProductMapping toUnit, UnitProductMapping fromUnit, List<UnitProductMapping> changedUnitProductMappings, Integer loggedInUser) {
        if(fromUnit == null) {
            if(toUnit.getProductStatus().equals(ProductStatus.ACTIVE.name())) {
                toUnit.setProductStatus(ProductStatus.IN_ACTIVE.name());
                toUnit.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
                changedUnitProductMappings.add(toUnit);
            }
            // update UnitProductPricing
            return markInActiveUnitProductPricing(toUnit.getUnitProductPricings(), loggedInUser);
        }
        if(!toUnit.getProductStatus().equals(fromUnit.getProductStatus())) {
            toUnit.setProductStatus(fromUnit.getProductStatus());
            toUnit.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
            changedUnitProductMappings.add(toUnit);
        }
        // update UnitProductPricing
        return mapUnitProductPricing(toUnit.getUnitProductPricings(), fromUnit.getUnitProductPricings(), loggedInUser, toUnit);
    }

    private void createNewUnitProductMapping(UnitProductMapping toUnit, UnitProductMapping fromUnit, List<UnitProductMapping> changedUnitProductMappings, UnitDetail toUnitDetail, Integer loggedInUser) {
        if(toUnit == null) {
            UnitProductMapping mapping = new UnitProductMapping();
            mapping.setUnitDetail(toUnitDetail);
            mapping.setProductStatus(fromUnit.getProductStatus());
            mapping.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
            mapping.setProductDetail(fromUnit.getProductDetail());
            mapping.setProductStartDate(fromUnit.getProductStartDate());
            mapping.setProductEndDate(fromUnit.getProductEndDate());
            changedUnitProductMappings.add(mapping);
        }
    }

    private Integer markInActiveUnitProductPricing(List<UnitProductPricing> prices, Integer loggedInUser) {
        if(CollectionUtils.isEmpty(prices)) {
            return 0;
        }
        List<UnitProductPricing> changedUpp = new ArrayList<>();
        for(UnitProductPricing upp : prices) {
            if(upp.getStatus().equals(ProductStatus.ACTIVE.name())) {
                upp.setStatus(ProductStatus.IN_ACTIVE.name());
                upp.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
                upp.setUpdatedBy(loggedInUser);
                changedUpp.add(upp);
            }
        }
        dao.updateUnitProductPricings(changedUpp);
        return changedUpp.size();
    }

    private int mapUnitProductPricing(List<UnitProductPricing> toUnitPricing, List<UnitProductPricing> fromUnitPricing, Integer loggedInUser, UnitProductMapping unitProdRef) {
        Map<Integer, UnitProductPricing> fromUnitPricingMap = fromUnitPricing.stream()
                .collect(Collectors.toMap(
                        upp -> upp.getRefLookup().getRlId(),
                        upp -> upp,
                        (existing, duplicate) -> existing
                ));

        int uppUpdatedCount = 0;

        List<UnitProductPricing> changedUpp = new ArrayList<>();

        for(UnitProductPricing upp : toUnitPricing) {
            if(fromUnitPricingMap.containsKey(upp.getRefLookup().getRlId())) {
                syncUPP(changedUpp, fromUnitPricingMap.get(upp.getRefLookup().getRlId()), upp, loggedInUser, unitProdRef);
                fromUnitPricingMap.remove(upp.getRefLookup().getRlId());
            } else {
                uppUpdatedCount += markInActiveUnitProductPricing(List.of(upp), loggedInUser);
            }
        }

        for(UnitProductPricing upp : fromUnitPricingMap.values()) {
            syncUPP(changedUpp, upp, null, loggedInUser, unitProdRef);
        }
        dao.updateUnitProductPricings(changedUpp);
        return uppUpdatedCount + changedUpp.size();
    }

    private void syncUPP(List<UnitProductPricing> changedUpp, UnitProductPricing fromUpp, UnitProductPricing toUpp, Integer loggedInUser, UnitProductMapping unitProdRef) {
        if(toUpp == null) {
            toUpp = new UnitProductPricing();
            toUpp.setUnitProductMapping(unitProdRef);
            toUpp.setAliasProductName(fromUpp.getAliasProductName());
            toUpp.setBuffer(fromUpp.getBuffer());
            toUpp.setCodCost(fromUpp.getCodCost());
            toUpp.setCost(fromUpp.getCost());
            toUpp.setThreshold(fromUpp.getThreshold());
            toUpp.setDimensionDescriptor(fromUpp.getDimensionDescriptor());
        }
        toUpp.setUpdatedBy(loggedInUser);
        toUpp.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
        toUpp.setStatus(fromUpp.getStatus());
        toUpp.setPrice(fromUpp.getPrice());
        toUpp.setRefLookup(fromUpp.getRefLookup());
        toUpp.setRecipeProfile(fromUpp.getRecipeProfile());
        toUpp.setIsDeliveryOnlyProduct(fromUpp.getIsDeliveryOnlyProduct());
        changedUpp.add(toUpp);
    }

    private void addNewSyncLog(int upmCount, int uppCount, UnitDetail toUnitDetail, UnitDetail fromUnitDetail, Integer loggedInUser) {
        String comment = "Unit Product Mapping was successFul from <b>" + fromUnitDetail.getUnitName() +
                "[ " + fromUnitDetail.getUnitId() + " ]</b> -> to <b>" + toUnitDetail.getUnitName() + "[ " + toUnitDetail.getUnitId() + " ]</b>" +
                ".\n No.of UnitProductMapping fields updated is : " + upmCount + "\n" +
                "No.of UnitProductPricing fields updated is : " + uppCount;

        UnitProductSyncLog unitProductSyncLog = new UnitProductSyncLog();
        unitProductSyncLog.setCreatedBy(loggedInUser);
        unitProductSyncLog.setFromUnit(fromUnitDetail);
        unitProductSyncLog.setToUnit(toUnitDetail);
        unitProductSyncLog.setComment(comment);
        unitProductSyncLog.setCreatedAt(AppUtils.getCurrentTimestamp());
        dao.saveLog(unitProductSyncLog);
        LOG.info("Unit sync was successful --> {}", comment);
    }
    @Override
    public List<BrandMapping> getPartnerMappingUnits(Integer brandId) {
        LOG.info("Getting brand mapping from UnitPartnerBrandMappingData ");
        IMap<UnitPartnerBrandKey, UnitPartnerBrandMappingData> unitMappedData = masterCache.getUnitPartnerBrandMappingMetaData();
        List<BrandMapping> all = new ArrayList<>();
        if (unitMappedData != null) {
            Map<Integer, Pair<Boolean, Boolean>> mapOfPair = new HashMap<>();
            Iterator<IMap.Entry<UnitPartnerBrandKey, UnitPartnerBrandMappingData>> iteratorOfUnitPartner = unitMappedData.entrySet().iterator();
            while (iteratorOfUnitPartner.hasNext()) {
                IMap.Entry<UnitPartnerBrandKey, UnitPartnerBrandMappingData> entry = iteratorOfUnitPartner.next();
                UnitPartnerBrandMappingData data = entry.getValue();
                if (data.getBrandId().equals(brandId)) {
                    if (mapOfPair.containsKey(data.getUnitId())) {
                        Pair<Boolean, Boolean> pairOfPartner = mapOfPair.get(data.getUnitId());
                        if (data.getPartnerId().equals(AppConstants.CHANNEL_PARTNER_ZOMATO) && data.getStatus().equals(AppConstants.ACTIVE)) {
                            pairOfPartner.setKey(true);
                        }
                        if (data.getPartnerId().equals(AppConstants.CHANNEL_PARTNER_SWIGGY) && data.getStatus().equals(AppConstants.ACTIVE)) {
                            pairOfPartner.setValue(true);
                        }
                        mapOfPair.put(data.getUnitId(), pairOfPartner);
                    } else {
                        Pair<Boolean, Boolean> pairOfPartner = new Pair<>();
                        pairOfPartner.setKey(false);
                        pairOfPartner.setValue(false);
                        if (data.getPartnerId().equals(AppConstants.CHANNEL_PARTNER_ZOMATO) && data.getStatus().equals(AppConstants.ACTIVE)) {
                            pairOfPartner.setKey(true);
                        }
                        if (data.getPartnerId().equals(AppConstants.CHANNEL_PARTNER_SWIGGY) && data.getStatus().equals(AppConstants.ACTIVE)) {
                            pairOfPartner.setValue(true);
                        }
                        mapOfPair.put(data.getUnitId(), pairOfPartner);
                    }
                }
            }
            Iterator<Map.Entry<Integer, Pair<Boolean, Boolean>>> iterator = mapOfPair.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Integer, Pair<Boolean, Boolean>> entry = iterator.next();
                Pair<Boolean, Boolean> pair = entry.getValue();
                BrandMapping ob = new BrandMapping(entry.getKey(), pair.getKey(), pair.getValue());
                all.add(ob);
            }
        }
        return all;
    }

    @Override
    public List<Brand> getUnitBrandMappings(Integer unitId) {
        return masterCache.getUnitBrandsMap().getOrDefault(unitId, new ArrayList<>());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean addUnitBrandMappings(Integer unitId, List<Brand> brands, Integer userId) {
        try {
            List<UnitBrandMapping> unitBrandMappings = unitBrandMappingDao.findByUnitId(unitId);
            List<Integer> brandsToActivate = brands.stream().map(Brand :: getBrandId).toList();
            Integer companyId = masterCache.getUnit(unitId).getCompany().getId();
            List<Integer> mappedBrands = masterCache.getCompanyBrandsMap().getOrDefault(companyId, new ArrayList<>())
                    .stream().map(Brand :: getBrandId).toList();
            List<UnitBrandMapping> newUnitBrandMappings = new ArrayList<>();
            if (unitBrandMappings.isEmpty()) {
                // New Unit has been Added
                for (Brand b : masterCache.getAllBrands()) {
                    newUnitBrandMappings.add(createNewUnitBrandMapping(unitId, b.getBrandId(), userId, brandsToActivate));
                }
            } else {
                // Existing Unit has been Updated
                Map<Integer, UnitBrandMapping> brandIdMapping = new HashMap<>();
                for (UnitBrandMapping ubm : unitBrandMappings) {
                    brandIdMapping.put(ubm.getBrandId(), ubm);
                }
                for (Brand b : masterCache.getAllBrands()) {
                    UnitBrandMapping ubm = brandIdMapping.get(b.getBrandId());
                    if (Objects.isNull(ubm)) {
                        newUnitBrandMappings.add(createNewUnitBrandMapping(unitId, b.getBrandId(), userId, brandsToActivate));
                    } else {
                        if (brandsToActivate.contains(b.getBrandId())) {
                            ubm.setStatus(AppConstants.ACTIVE);
                        } else {
                            ubm.setStatus(AppConstants.IN_ACTIVE);
                        }
                        if (mappedBrands.contains(b.getBrandId())) {
                            ubm.setUpdatedBy(userId);
                            ubm.setUpdationTimestamp(AppUtils.getCurrentTimestamp());
                        }
                        newUnitBrandMappings.add(ubm);
                    }
                }
            }
            unitBrandMappingDao.saveAllAndFlush(newUnitBrandMappings);
            cache.refreshUnitBrandMaps();
            return Boolean.TRUE;
        } catch (Exception e) {
            LOG.info("Error occurred while adding unit brand mappings for unit ID : {}", unitId);
            return Boolean.FALSE;
        }
    }

    private UnitBrandMapping createNewUnitBrandMapping(Integer unitId, Integer brandId, Integer userId,
                                                       List<Integer> brandsToActivate) {
        UnitBrandMapping ubm = new UnitBrandMapping();
        ubm.setUnitId(unitId);
        ubm.setBrandId(brandId);
        ubm.setCreationTimestamp(AppUtils.getCurrentTimestamp());
        ubm.setCreatedBy(userId);
        if (brandsToActivate.contains(brandId)) {
            ubm.setStatus(AppConstants.ACTIVE);
        } else {
            ubm.setStatus(AppConstants.IN_ACTIVE);
        }
        return ubm;
    }

}
