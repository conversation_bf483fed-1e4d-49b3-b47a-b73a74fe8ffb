package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.joda.time.LocalDateTime;
import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * Entity class for UNIT_CONTACT_DATA table
 */
@Entity
@Table(name = "UNIT_CONTACT_DATA")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitContactDataEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "REFERENCE_NAME", length = 100)
    private String referenceName;

    @Column(name = "CONTACT_NUMBER")
    private Long contactNumber;

    @Column(name = "STATUS", length = 45)
    private String status;

    @Column(name = "CREATED_BY", length = 45)
    private Integer createdBy;

    @Column(name = "CREATED_ON")
    private LocalDateTime createdOn;

    @Column(name = "UPDATED_BY", length = 45)
    private Integer updatedBy;

    @Column(name = "UPDATED_ON")
    private LocalDateTime updatedOn;

    @Override
    public String toString() {
        return "UnitContactDataEntity{" +
                "id=" + id +
                ", unitId=" + unitId +
                ", referenceName='" + referenceName + '\'' +
                ", contactNumber=" + contactNumber +
                ", status='" + status + '\'' +
                ", createdBy='" + createdBy + '\'' +
                ", createdOn=" + createdOn +
                ", updatedBy='" + updatedBy + '\'' +
                ", updatedOn=" + updatedOn +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        UnitContactDataEntity that = (UnitContactDataEntity) o;

        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
