<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .blinking-red-border {
        border: 5px solid red; /* Red border */
        animation: blink-border-animation 1s ease-in-out infinite;
    }

    .unit {
        background-color: green;
        color: white;
    }

    .product {
        background-color: purple;
        color: white;
    }

    @keyframes blink-border-animation {
        0%, 100% {
            border-color: red; /* Border disappears */
        }
        50% {
            border-color: transparent;
        }
    }
</style>

<div flash-message="5000"></div>

<div class="container-fluid" data-ng-init="init()">
    <div style="display: flex;flex-direction: row;width:100%;justify-content: space-evenly;height:100%; flex-wrap: wrap">

        <div style="width:49%;" >


        <div class="row">
            <form class="navbar-form bs-navbar-top-example navbar-static-top"
                  style="margin-top: 20px" role="search" name="searchOrderForm">
                <button type="button" class="btn btn-warning pull-left"
                        style="margin-left: 50px" data-ng-click="backToCODCover()">Back</button>
                <button type="button" class="btn btn-warning pull-right"
                        style="margin-left: 50px" data-ng-click="getPendingOrders(true, true)">Refresh</button>
                <h2 class="text-center" style="color: #737370;text-align: center;">Partner Order Dashboard</h2>
            </form>
        </div>
        <div class="row">
            <div class="col-xs-12">

                <div data-ng-repeat="order in orderList | orderBy : '-addTime'" style="border: #ccc 1px solid; background: #fff;">
                    <div class="row" data-ng-click="showDetail(order)"
                         style="padding: 10px; margin: 0; background: #efefef; border-bottom: #ccc 1px solid;  cursor: pointer;">
                        <div class="col-xs-2"
                             style="color: orange; font-weight: bold"
                             data-ng-if="order.partnerName == 'SWIGGY'">
                            <label>Partner:</label> {{order.partnerName}}
                        </div>
                        <div class="col-xs-2"
                             style="color: red; font-weight: bold"
                             data-ng-if="order.partnerName == 'ZOMATO'">
                            <label>Partner:</label> {{order.partnerName}}
                        </div>
                        <div class="col-xs-2"
                             style="color: black; font-weight: bold "
                             data-ng-if="order.partnerName != 'ZOMATO' && order.partnerName != 'SWIGGY' ">
                            <label>Partner:</label> {{order.partnerName}}
                        </div>

                        <div class="col-xs-2"
                             style="color: green; font-weight: bold"
                             data-ng-if="order.brand.brandName == 'Chaayos'">
                            <label>Brand:</label> {{order.brand.brandName}}
                        </div>
                        <div class="col-xs-2"
                             style="color: yellow; font-weight: bold"
                             data-ng-if="order.brand.brandName == 'Ghee and Turmeric'">
                            <label>Brand:</label> {{order.brand.brandName}}
                        </div>
                        <div class="col-xs-2"
                             style="color: black; font-weight: bold"
                             data-ng-if="order.brand.brandName != 'Ghee and Turmeric' && order.brand.brandName != 'Chaayos' ">
                            <label>Brand:</label> {{order.brand.brandName}}
                        </div>
                        <div class="col-xs-1">
                            <label>Order Id:</label> {{order.partnerOrderId}}
                        </div>
                        <div class="col-xs-2" data-ng-if="order.partnerName != 'MAGICPIN'">
                            <label>Outlet:</label> {{outlets[order.partnerOrder.outlet_id].name}}
                        </div>
                        <div class="col-xs-2" data-ng-if="order.partnerName == 'MAGICPIN'">
                            <label>Outlet:</label> {{outlets[order.partnerOrder.merchantData.client_id].name}}
                        </div>
                        <div class="col-xs-2">
                            <label>Order Time:</label> {{order.addTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}
                        </div>
                        <div class="col-xs-2"
                             data-ng-if="order.partnerName == 'SWIGGY' && order.partnerOrder.tags && order.partnerOrder.tags.length > 0">
                            <label>Order Tags:</label> {{order.partnerOrder.tags.join(', ')}}
                        </div>
                        <div class="col-xs-1" data-ng-if="order.partnerName == 'SWIGGY'">
                            <label>Bill Value:</label> {{order.partnerOrder.restaurant_gross_bill - order.partnerOrder.restaurant_discount}}
                        </div>
                        <div class="col-xs-1" data-ng-if="order.partnerName == 'ZOMATO'">
                            <label>Bill Value:</label> {{order.partnerOrder.total_merchant}}
                        </div>
                        <div class="col-xs-1" data-ng-if="order.partnerName == 'MAGICPIN'">
                            <label>Bill Value:</label> {{order.partnerOrder.amount - order.partnerOrder.tax - order.partnerOrder.merchantFundedDiscount}}
                        </div>
                        <div class="col-xs-1">
                            <label>Error Count:</label> {{order.orderErrors == null ? 0 : order.orderErrors.length}}
                        </div>
                        <div class="col-xs-1">
                            <label>Status:</label> {{order.partnerOrderStatus}}
                        </div>
                    </div>
                    <div data-ng-show="order.showDetail">
                        <div class="alert alert-info" data-ng-if="order.partnerOrderStatus == 'EDIT_CANCEL_REQUESTED'">
                            Please cancel this order. Generated order id: {{order.kettleOrder.generateOrderId}}
                        </div>
                        <div class="alert alert-info" data-ng-if="order.partnerOrderStatus == 'CANCEL_REQUESTED'">
                            Please cancel this order. Partner order id: {{order.partnerOrderId}}
                        </div>
                        <div class="row"
                             style="padding: 10px; margin: 0; background: #efefef; border-bottom: #ccc 1px solid;  cursor: pointer;">
                            <div class="col-xs-1" style="margin-left:20px;" data-ng-if="order.partnerName == 'SWIGGY'">
                                <label>Partner CustomerId:</label> {{order.partnerOrder.customer_id}}
                            </div>
                            <div class="col-xs-1" data-ng-if="order.partnerName == 'ZOMATO'">
                                <label>Customer Name:</label> {{order.partnerOrder.customer_details.name}}
                                </div>
                            <div class="col-xs-1" data-ng-if="order.partnerName == 'ZOMATO'">
                                <label>Customer's Phone Number:</label> {{order.partnerOrder.customer_details.phone_number}}
                            </div>
                            <div class="col-xs-5" style="margin-left:40px;" data-ng-if="order.partnerName == 'ZOMATO'">
                                <label>Customer's Address:</label> {{order.partnerOrder.customer_details.address}} {{order.partnerOrder.customer_details.delivery_area}}
                            </div>
                            <div class="col-xs-1" data-ng-if="order.partnerName == 'ZOMATO'">
                                <label>Customer Id:</label> {{order.partnerOrder.order_id+"_"+order.partnerOrder.customer_details.phone_number}}
                            </div>
                            <div class="col-xs-4" data-ng-if="order.partnerName == 'MAGICPIN'">
                                <label>Customer Name:</label> {{order.partnerOrder.shippingAddress.name}}
                            </div>
                            <div class="col-xs-4" data-ng-if="order.partnerName == 'MAGICPIN'">
                                <label>Customer's Phone Number:</label> {{order.partnerOrder.shippingAddress.contactNumbers[0]}}
                            </div>
                            <div class="order-info pull-right">
                                <p>
                                    <label>Picked By:</label> <strong>{{ order.fallbackProcessedBy }}</strong>
                                </p>
                                <p>
                                    Current State: <strong>{{order.currentState}}</strong>
                                </p>
                                    <button class="btn btn-primary" ng-click="fallbackProcess(order)">Pick Up</button>
                            </div>

                        </div>
                        <div class="row" style="margin: 0; padding: 10px;">
                            <div class="col-xs-12">
                                <ul>
                                    <li style="font-weight: bold; color: black" data-ng-repeat="error in order.orderErrors" data-ng-if="error.errorCode == 'STOCK_NOT_SUFFICIENT' || error.errorCode == 'STOCK_NOT_AVAILABLE' || error.errorCode == 'STOCK_NOT_FOUND'">{{error.errorCode}}: {{error.errorDescription}}</li>
                                </ul>
                            </div>
                        </div>
                        <div class="row" style="margin: 0; padding: 10px;">
                            <div class="col-xs-12">
                                <ul>
                                    <li style="color: gray" data-ng-repeat="error in order.orderErrors" data-ng-if="error.errorCode != 'STOCK_NOT_SUFFICIENT' && error.errorCode != 'STOCK_NOT_AVAILABLE' && error.errorCode != 'STOCK_NOT_FOUND'">{{error.errorCode}}: {{error.errorDescription}}</li>
                                </ul>
                            </div>
                        </div>
                        <div data-ng-show="order.showItems && order.partnerName == 'SWIGGY'" style="margin : 2px;padding: 10px; background: #efefef; ">
                            <div class="row" style="padding :10px ; border-bottom: #ccc 1px solid;" >
                                <div class="col-xs-2" style="color: green; font-weight: bold">Item</div>
                                <div class="col-xs-2" style="color: green; font-weight: bold">Quantity</div>
                                <div class="col-xs-4" style="color: green; font-weight: bold">Addons</div>
                                <div class="col-xs-4" style="color: green; font-weight: bold">Variants</div>
                            </div>
                            <div class="row border border-primary " style="padding : 10px ; border-bottom: #ccc 1px solid;" data-ng-repeat="item in order.partnerOrder.items">
                                <div class="col-xs-2" style="font-weight: bold">{{item.name}} </div>
                                <div class="col-xs-2" style="font-weight: bold">{{item.quantity}}</div>
                                <div class="col-xs-4" style="font-weight: bold"><span data-ng-repeat="addon in item.addons" >{{addon.name}},</span></div>
                                <div class="col-xs-4" style="font-weight: bold"><span data-ng-repeat="variant in item.variants" >{{variant.name}},</span></div>
                            </div>
                        </div>

                        <div style="padding-left:10px;padding-top:5px;">
                            <button class="btn btn-primary" ng-click="openModal('cancel',order)" title="Cancellation Reason" style="border-radius:50%;">X</button>
                            <button class="btn btn-primary" ng-click="openModal('customer',order)" title="Customer Response" style="border-radius:50%;"><i class="fa fa-user"></i></button>
                            <button class="btn btn-primary" ng-click="openModal('partner',order)" title = "Partner Response" style="border-radius:50%;"><i class="fa fa-shopping-cart"></i></button>
                            <button class="btn btn-primary" ng-click="openContactModal(order)" title="Contact Information" style="border-radius:50%;"><i class="fa fa-phone"></i></button>
                        </div>

                        <!-- Reusable Modal -->
                        <div class="modal modal-mx" ng-show="showModal" ng-click="closeDropdownOnOutsideClick($event)" style="display: block; background: rgba(0,0,0,0.5); position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 1000;">
                            <div ng-click="handleModalContentClick($event)" style="display:flex; flex-direction:column; background: white; padding: 20px; margin: 100px auto; width: 400px; border-radius: 8px; position: relative;">
                                <h5>Select Option</h5>

                                <div style="position: relative; width: 100%; margin-bottom: 20px;" id="dropdown-container" ng-click="$event.stopPropagation()">
                                    <label>{{ currentDropdown.label }}</label>

                                    <div ng-click="toggleDropdown()"
                                         style="border: 1px solid #ccc; padding: 8px; cursor: pointer; background: white; min-height: 20px;">
                                        {{ currentDropdown.model || "Select an option" }}
                                        <span style="float: right;">▼</span>
                                    </div>

                                    <div ng-show="dropdownOpen"
                                         style="position: absolute; background: white; border: 1px solid #ccc; width: 100%; max-height: 300px; overflow-y: auto; z-index: 1001; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">

                                        <input type="text"
                                               ng-model="searchText"
                                               placeholder="Search..."
                                               ng-click="$event.stopPropagation()"
                                               class="form-control"
                                               style="margin: 5px; width: calc(100% - 10px);" />

                                        <div ng-repeat="option in currentDropdown.options | filter: searchText"
                                             ng-click="selectOption(option); $event.stopPropagation()"
                                             style="padding: 8px; cursor: pointer; border-top: 1px solid #eee;"
                                             ng-class="{'selected': option === currentDropdown.model}">
                                            {{ option }}
                                        </div>
                                    </div>
                                </div>
                                <div ng-show="isOtherSelected" style="margin-bottom: 20px;" ng-click="$event.stopPropagation()">
                                    <label>Enter Custom Reason:</label>
                                    <textarea ng-model="modalData.otherText"
                                              class="form-control"
                                              placeholder="Enter your reason here..."
                                              rows="2"
                                              style="width: 100%; resize: vertical;"></textarea>
                                </div>

                                <div style="display:flex; justify-content: space-between; gap: 10px;">
                                    <button class="btn btn-secondary" ng-click="closeModal()" style="height: 40px; flex: 1;">Close</button>
                                    <button class="btn btn-primary" ng-click="submitModal(order)" style="height: 40px; flex: 1;">Submit</button>
                                </div>
                            </div>
                        </div>

                        <style>
                            .selected {
                                background-color: #e6f3ff !important;
                            }

                            /* Close dropdown when clicking outside */
                            .modal-mx .form-control:focus {
                                outline: none;
                                border-color: #007bff;
                                box-shadow: 0 0 0 2px rgba(0,123,255,.25);
                            }
                        </style>

                        <!-- Contact Information Modal -->
                        <div class="modal modal-mx" ng-show="showContactModal" ng-click="closeContactModal($event)" style="display: block; background: rgba(0,0,0,0.5); position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 1000;">
                            <div ng-click="handleContactModalContentClick($event)" style="display:flex; flex-direction:column; background: white; padding: 20px; margin: 100px auto; width: 500px; border-radius: 8px; position: relative;">
                                <h5>Contact Information</h5>

                                <!-- Contact Table -->
                                <div style="margin-bottom: 20px;">
                                    <table style="width: 100%; border-collapse: collapse; border: 1px solid #ccc;">
                                        <thead>
                                            <tr style="background: #f5f5f5;">
                                                <th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Contact Person</th>
                                                <th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Contact Number</th>
                                                <th style="border: 1px solid #ccc; padding: 8px; text-align: center; width: 80px;">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr ng-repeat="contact in contactList track by $index" style="border-bottom: 1px solid #eee;">
                                                <td style="border: 1px solid #ccc; padding: 8px;">
                                                    <input type="text" ng-model="contact.person" class="form-control" placeholder="Enter contact person" style="border: none; background: transparent; width: 100%;">
                                                </td>
                                                <td style="border: 1px solid #ccc; padding: 8px;">
                                                    <input type="text" ng-model="contact.number" class="form-control" placeholder="Enter contact number" style="border: none; background: transparent; width: 100%;">
                                                </td>
                                                <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">
                                                    <button class="btn btn-danger btn-sm" ng-click="removeContact($index)" style="padding: 2px 6px; font-size: 12px;">Remove</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Add Contact Button -->
                                <div style="margin-bottom: 20px; text-align: center;">
                                    <button class="btn btn-success" ng-click="addContact()" style="padding: 8px 16px;">Add Contact</button>
                                </div>

                                <!-- Modal Footer Buttons -->
                                <div style="display:flex; justify-content: space-between; gap: 10px;">
                                    <button class="btn btn-secondary" ng-click="closeContactModal()" style="height: 40px; flex: 1;">Close</button>
                                    <button class="btn btn-primary" ng-click="saveContacts()" style="height: 40px; flex: 1;">Save</button>
                                </div>
                            </div>
                        </div>


                        <div data-ng-show="order.showItems && order.partnerName == 'MAGICPIN'" style="margin : 2px;padding: 10px; background: #efefef; ">
                            <div class="row" style="padding :10px ; border-bottom: #ccc 1px solid;" >
                                <div class="col-xs-2" style="color: green; font-weight: bold">Item</div>
                                <div class="col-xs-1" style="color: green; font-weight: bold">Quantity</div>
                                <div class="col-xs-3" style="color: green; font-weight: bold">Addons</div>
                                <div class="col-xs-3" style="color: green; font-weight: bold">Paid Addons</div>
                                <div class="col-xs-3" style="color: green; font-weight: bold">Upselling Items</div>
                            </div>
                            <div class="row border border-primary " style="padding : 10px ; border-bottom: #ccc 1px solid;" data-ng-repeat="item in order.items">
                                <div class="col-xs-2" style="font-weight: bold">{{item.itemName}} </div>
                                <div class="col-xs-1" style="font-weight: bold">{{item.quantity}}</div>
                                <div class="col-xs-3" style="font-weight: bold"><span data-ng-repeat="addon in item.addonProducts" >{{addon.name}},</span></div>
                                <div class="col-xs-3" style="font-weight: bold"><span data-ng-repeat="paidAddon in item.paidAddonProducts" >{{paidAddon.name}},</span></div>
                                <div class="col-xs-3" style="font-weight: bold"><span data-ng-repeat="upsellingItem in item.recommendedProducts" >{{upsellingItem.name}},</span></div>
                            </div>
                        </div>
                        <div class="row" style="padding: 10px;">
                            <div class="col-xs-12 text-right">
                                <div class="left"  data-ng-if="order.partnerName == 'SWIGGY' || order.partnerName == 'MAGICPIN'">
                                    <button class="btn btn-primary" data-ng-if="order.showItems == false || order.showItems == null" data-ng-click="toggleItems(order)">Show Items</button>
                                    <button class="btn btn-primary" data-ng-if="order.showItems == true" data-ng-click="toggleItems(order)">Hide Items</button>
                                </div>
                                <button class="btn btn-primary" data-ng-if="order.partnerName == 'SWIGGY' && order.partnerOrderStatus != 'EDIT_CANCEL_REQUESTED' &&
                                order.partnerOrderStatus != 'CANCEL_REQUESTED'" data-ng-click="callSwiggySupport(order)">Call Swiggy Support</button>
                                <button class="btn btn-primary" data-ng-if="order.partnerName == 'SWIGGY'" data-ng-click="sendStockOutNotification(order)" >Stock Out Notification </button>
                                <button class="btn btn-primary" data-ng-if="order.partnerName == 'SWIGGY' && order.partnerOrderStatus != 'CANCEL_REQUESTED'"
                                        data-ng-click="connectCustomer(order)">Call Swiggy Customer</button>
                                <button class="btn btn-primary" data-ng-if="order.partnerOrderStatus == 'CHECKED'"
                                        data-ng-click="markResolved(order, $index)">Mark Resolved</button>
                                <!--<button class="btn btn-primary" data-ng-if="order.partnerOrderStatus == 'EDIT_CANCEL_REQUESTED' ||
                                order.partnerOrderStatus == 'CANCEL_REQUESTED'" data-ng-click="cancelledInKettle(order)">Cancel in Kettle</button>-->
                                <button class="btn btn-primary" data-ng-if="isManualProcess(order)" data-ng-click="manualProcess(order, $index)">Manual Process</button>
                                <button class="btn btn-primary" data-ng-click="forceProcess(order, $index)">Force Process</button>
                                <button class="btn btn-primary" data-ng-click="loadInventory(order.unitId,order.brandId,order.partnerName)">View Inventory</button>
                                <button class="btn btn-primary" data-ng-if="order.partnerName == 'ZOMATO'" data-ng-click="getCustomerDetailData(order)">Get Customer Detail</button>
                                <button class="btn btn-danger" data-ng-click="openRejectOrderModal(order)">Reject Order</button>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>

        </div>

        <div style="border-style: solid; border-color: black;width: 1px;"></div>

        <div style="width:49%;">
            <div class="row">
                <form class="navbar-form bs-navbar-top-example navbar-static-top"
                      style="margin-top: 20px" role="search" name="searchOrderForm">
<!--                    <button type="button" class="btn btn-warning pull-left"-->
<!--                            style="margin-left: 50px" data-ng-click="backToCODCover()">Back</button>-->

                    <button type="button" class="btn btn-warning pull-right"
                            style="margin-left: 50px" data-ng-click="refreshUnitOffRequests(true)">Refresh</button>
                    <button type="button" class="btn btn-warning pull-right"
                            style="margin-left: 50px" data-toggle="modal" data-target="#reportGenerationModal">Generate Report</button>
                    <h2 class="text-center" style="color: #737370;text-align: center;">Unit Switch OFF Requests Dashboard</h2>
                </form>
            </div>
            <!-- modal for report generation -->
            <div
                    class="modal fade"
                    id="reportGenerationModal"
                    tabindex="-1"
                    role="dialog"
                    aria-labelledby="myModalLabel">
                <div
                        class="modal-dialog"
                        role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button
                                    type="button"
                                    class="close"
                                    data-dismiss="modal"
                                    aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>

                            <h4
                                    class="modal-title"
                                    id="myModalLabel">{{action}}Report Generation</h4>
                        </div>
                        <div class="form-group" style="margin-left: 20px">
                            <label>Select Number of days</label>
                            <input type="number"
                                   step="1"
                                   min="0"
                                   max="7"
                                   class="form-control"
                                   ng-model="noOfDays"
                                   ng-change=""
                            required>
                        </div>
                        <div class="row" style="display: flex;justify-content: center;margin-bottom: 10px;">
                            <button
                                    class="btn btn-primary pull-right"
                                    data-toggle="modal"
                                    id="downloadUnitClosureSheet"
                                    ng-click="downloadUnitClosureSheet(noOfDays)">
                                <i class="fa fa-download fw"></i> Download Unit Closure Report
                            </button>
                        </div>

                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div data-ng-repeat="requests in UnitOffRequestList "  style="border: #ccc 1px solid; background: #fff;">
                        <div class="row"
                             style="padding: 10px; margin: 0; background: #efefef; cursor: pointer;"
                             data-ng-class="{'blinking-red-border': requests.color == 'red'}">
                            <div class="col-xs-1">
                                <label>Unit ID:</label> {{requests.unitId}}
                            </div>
                            <div class="col-xs-1">
                                <label>Channel Partner ID:</label> {{requests.channelPartnerId}}
                            </div>
                            <div class="col-xs-1">
                                <label>Cafe Name:</label> {{requests.cafeName}}
                            </div>
                            <div class="col-xs-1">
                                <label>Partner Name:</label> {{requests.channelPartner}}
                            </div>
                            <div class="col-xs-1" data-ng-if="requests.brandId==1">
                                <label>Brand:</label> {{"Chaayos"}}
                            </div>
                            <div class="col-xs-1" data-ng-if="requests.brandId==3">
                                <label>Brand:</label> {{"GNT"}}
                            </div>
                            <div class="col-xs-1">
                                <label>Reason:</label> {{requests.reason}}
                            </div>
                            <div class="col-xs-1">
                                <label>Start Time:</label> {{requests.startTime}}
                            </div>
                            <div class="col-xs-1">
                                <label>End Time:</label> {{requests.endTime}}
                            </div>
                            <div  class="col-xs-2"  data-ng-if="requests.color == 'red'" style="color:red;">
                                <label>Date:</label> {{requests.actionTimestamp}}
                            </div>
                            <div  class="col-xs-2"  data-ng-if="requests.color == 'black'" style="color:#333333;">
                                <label>Date:</label> {{requests.actionTimestamp}}
                            </div>
                            <div class="col-xs-1">
                                <label>Emp ID:</label> {{requests.requestedByEmpId}}
                            </div>
                            <div class="col-xs-1">
                                <label>Dam ID:</label> {{requests.acceptedByEmpId}}
                            </div>
                            <div class="col-xs-1">
                                <label>Status:</label> {{requests.actionTaken}}
                            </div>
                            <div class="row" style="padding: 10px;">
                                <div class="col-xs-12 text-right">
                                    <button class="btn btn-primary" type="button"
                                            data-ng-click="forceCafeShutdownRequestRemoval(requests)">Force Remove</button>
                                    <button class="btn btn-success"  data-ng-click="deactivateUnitUrl(requests)">Turn On</button>
                                </div>
                                <div class="col-xs-4" data-ng-if="requests.requestType != null">
                                    <label>Request Type:</label>
                                    <p  style="width: 100px; text-align: center; padding: 5px 0px; border-radius: 5px;" 
                                        data-ng-class="requests.requestType == 'UNIT' ? 'unit' : 'product'">
                                        {{requests.requestType}}
                                    </p>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>

            </div>
        </div>

<!--        <div style="border-style: solid;border-color: black;width:49%;">-->

<!--            <div class="row">-->
<!--                <form class="navbar-form bs-navbar-top-example navbar-static-top"-->
<!--                      style="margin-top: 20px" role="search" name="searchOrderForm">-->
<!--                    <button type="button" class="btn btn-warning pull-left"-->
<!--                            style="margin-left: 50px" data-ng-click="backToCODCover()">Back</button>-->
<!--                    <button type="button" class="btn btn-warning pull-right"-->
<!--                            style="margin-left: 50px" data-ng-click="getPendingOrders(true, true)">Refresh</button>-->
<!--                    <h2 class="text-center" style="color: #737370;text-align: center;">Unit Switch OFF Requests Dashboard</h2>-->
<!--                </form>-->
<!--            </div>-->

<!--            <div class="row">-->
<!--                <div class="col-xs-12">-->

<!--                    <div data-ng-repeat="order in orderList | orderBy : '-addTime'" style="border: #ccc 1px solid; background: #fff;">-->
<!--                        <div class="row" data-ng-click="showDetail(order)"-->
<!--                             style="padding: 10px; margin: 0; background: #efefef; border-bottom: #ccc 1px solid;  cursor: pointer;">-->
<!--                            <div class="col-xs-2"-->
<!--                                 style="color: orange; font-weight: bold"-->
<!--                                 data-ng-if="order.partnerName == 'SWIGGY'">-->
<!--                                <label>Partner:</label> {{order.partnerName}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-2"-->
<!--                                 style="color: red; font-weight: bold"-->
<!--                                 data-ng-if="order.partnerName == 'ZOMATO'">-->
<!--                                <label>Partner:</label> {{order.partnerName}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-2"-->
<!--                                 style="color: black; font-weight: bold "-->
<!--                                 data-ng-if="order.partnerName != 'ZOMATO' && order.partnerName != 'SWIGGY' ">-->
<!--                                <label>Partner:</label> {{order.partnerName}}-->
<!--                            </div>-->

<!--                            <div class="col-xs-2"-->
<!--                                 style="color: green; font-weight: bold"-->
<!--                                 data-ng-if="order.brand.brandName == 'Chaayos'">-->
<!--                                <label>Brand:</label> {{order.brand.brandName}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-2"-->
<!--                                 style="color: yellow; font-weight: bold"-->
<!--                                 data-ng-if="order.brand.brandName == 'Ghee and Turmeric'">-->
<!--                                <label>Brand:</label> {{order.brand.brandName}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-2"-->
<!--                                 style="color: black; font-weight: bold"-->
<!--                                 data-ng-if="order.brand.brandName != 'Ghee and Turmeric' && order.brand.brandName != 'Chaayos' ">-->
<!--                                <label>Brand:</label> {{order.brand.brandName}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-1">-->
<!--                                <label>Order Id:</label> {{order.partnerOrderId}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-2">-->
<!--                                <label>Outlet:</label> {{outlets[order.partnerOrder.outlet_id].name}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-2">-->
<!--                                <label>Order Time:</label> {{order.addTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-1" data-ng-if="order.partnerName == 'SWIGGY'">-->
<!--                                <label>Bill Value:</label> {{order.partnerOrder.restaurant_gross_bill - order.partnerOrder.restaurant_discount}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-1" data-ng-if="order.partnerName == 'ZOMATO'">-->
<!--                                <label>Bill Value:</label> {{order.partnerOrder.total_merchant}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-1">-->
<!--                                <label>Error Count:</label> {{order.orderErrors == null ? 0 : order.orderErrors.length}}-->
<!--                            </div>-->
<!--                            <div class="col-xs-1">-->
<!--                                <label>Status:</label> {{order.partnerOrderStatus}}-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div data-ng-show="order.showDetail">-->
<!--                            <div class="alert alert-info" data-ng-if="order.partnerOrderStatus == 'EDIT_CANCEL_REQUESTED'">-->
<!--                                Please cancel this order. Generated order id: {{order.kettleOrder.generateOrderId}}-->
<!--                            </div>-->
<!--                            <div class="alert alert-info" data-ng-if="order.partnerOrderStatus == 'CANCEL_REQUESTED'">-->
<!--                                Please cancel this order. Partner order id: {{order.partnerOrderId}}-->
<!--                            </div>-->
<!--                            <div class="row"-->
<!--                                 style="padding: 10px; margin: 0; background: #efefef; border-bottom: #ccc 1px solid;  cursor: pointer;">-->
<!--                                <div class="col-xs-1" data-ng-if="order.partnerName == 'SWIGGY'">-->
<!--                                    <label>Partner CustomerId:</label> {{order.partnerOrder.customer_id}}-->
<!--                                </div>-->
<!--                                <div class="col-xs-1" data-ng-if="order.partnerName == 'ZOMATO'">-->
<!--                                    <label>Customer Name:</label> {{order.partnerOrder.customer_details.name}}-->
<!--                                </div>-->
<!--                                <div class="col-xs-1" data-ng-if="order.partnerName == 'ZOMATO'">-->
<!--                                    <label>Customer's Phone Number:</label> {{order.partnerOrder.customer_details.phone_number}}-->
<!--                                </div>-->
<!--                                <div class="col-xs-4" data-ng-if="order.partnerName == 'ZOMATO'">-->
<!--                                    <label>Customer's Address:</label> {{order.partnerOrder.customer_details.address}} {{order.partnerOrder.customer_details.delivery_area}}                        </div>-->
<!--                            </div>-->
<!--                            <div class="row" style="margin: 0; padding: 10px;">-->
<!--                                <div class="col-xs-12">-->
<!--                                    <ul>-->
<!--                                        <li style="font-weight: bold; color: black" data-ng-repeat="error in order.orderErrors" data-ng-if="error.errorCode == 'STOCK_NOT_SUFFICIENT' || error.errorCode == 'STOCK_NOT_AVAILABLE' || error.errorCode == 'STOCK_NOT_FOUND'">{{error.errorCode}}: {{error.errorDescription}}</li>-->
<!--                                    </ul>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="row" style="margin: 0; padding: 10px;">-->
<!--                                <div class="col-xs-12">-->
<!--                                    <ul>-->
<!--                                        <li style="color: gray" data-ng-repeat="error in order.orderErrors" data-ng-if="error.errorCode != 'STOCK_NOT_SUFFICIENT' && error.errorCode != 'STOCK_NOT_AVAILABLE' && error.errorCode != 'STOCK_NOT_FOUND'">{{error.errorCode}}: {{error.errorDescription}}</li>-->
<!--                                    </ul>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="row" style="padding: 10px;">-->
<!--                                <div class="col-xs-12 text-right">-->
<!--                                    <button class="btn btn-primary" data-ng-if="order.partnerName == 'SWIGGY' && order.partnerOrderStatus != 'EDIT_CANCEL_REQUESTED' &&-->
<!--                                order.partnerOrderStatus != 'CANCEL_REQUESTED'" data-ng-click="callSwiggySupport(order)">Call Swiggy Support</button>-->
<!--                                    <button class="btn btn-primary" data-ng-if="order.partnerOrderStatus == 'CHECKED'"-->
<!--                                            data-ng-click="markResolved(order, $index)">Mark Resolved</button>-->
<!--                                    &lt;!&ndash;<button class="btn btn-primary" data-ng-if="order.partnerOrderStatus == 'EDIT_CANCEL_REQUESTED' ||-->
<!--                                    order.partnerOrderStatus == 'CANCEL_REQUESTED'" data-ng-click="cancelledInKettle(order)">Cancel in Kettle</button>&ndash;&gt;-->
<!--                                    <button class="btn btn-primary" data-ng-if="isManualProcess(order)" data-ng-click="manualProcess(order, $index)">Manual Process</button>-->
<!--                                    <button class="btn btn-primary" data-ng-click="forceProcess(order, $index)">Force Process</button>-->
<!--                                    <button class="btn btn-primary" data-ng-click="loadInventory(order.unitId)">View Inventory</button>-->

<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->

<!--                    </div>-->

<!--                </div>-->
<!--        </div>-->

<!--        </div>-->

    </div>
</div>


<div
        class="modal fade"
        id="customerDetailModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        data-ng-click="onCloseCustomerDetail()"
                        aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4
                        class="modal-title"
                        id="myBulkModalLabel">Customer Detail</h4>
            </div>
            <div class="modal-body">
                <div data-ng-repeat="detail in customerDetailString">
                    <p><strong>{{detail}}</strong></p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="rejectOrderModal" tabindex="-1" role="dialog"
     aria-labelledby="rejectOrderLabel" aria-hidden="true"
     data-ng-class="{'show': showRejectionModal}"
     data-ng-style="{'display': showRejectionModal ? 'block' : 'none'}">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="display: flex; align-items: center; justify-content: space-between;">
                <h4 class="modal-title" id="rejectOrderLabel">Reject Order</h4>
                <button type="button" class="close" data-ng-click="closeRejectOrderModal()">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <label for="reason" style="margin-bottom: 10px;">Rejection Reason</label>
                        <select name="rejectionReason" id="reason" class="form-control" data-ng-model="rejectionObject"
                                data-ng-options="reason as reason.message for reason in rejectionReasons" data-ng-change="onRejectionReasonChange()">
                            <option value="">Select a reason</option>
                        </select>
                    </div> 
                    <div class="form-group" data-ng-if="rejectionObject.id == '1'" style="display: flex; flex-direction: column;">
                        <label style="margin-bottom: 10px;">Select Item(s) which are Out of Stock</label>
                        <div data-ng-repeat="item in rejectionItems">
                            <input type="checkbox" id="{{item.catalogueId}}" data-ng-model="item.selected">
                            <label for="{{item.catalogueId}}">{{item.catalogueName}}</label>
                        </div>
                    </div>                   
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-ng-click="closeRejectOrderModal()">Cancel</button>
                <button type="button" class="btn btn-danger" data-ng-click="submitRejectOrder()">Submit</button>
            </div>
        </div>
    </div>
</div>

